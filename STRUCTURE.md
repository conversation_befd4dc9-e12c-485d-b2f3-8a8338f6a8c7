# SevlteFurpa Project Structure

This document provides an overview of the SevlteFurpa project structure, a desktop application built with Electron.js, Svelte, Vite, and Bulma CSS framework.

## Overview

SevlteFurpa is an Electron.js desktop application designed for point-of-sale (POS) operations with features including:

- User authentication
- Inventory management
- Sales processing
- Payment handling (including split payments)
- RabbitMQ integration for real-time data synchronization
- Local SQLite database for offline operation
- Customer display screen

## Technology Stack

- **Electron.js** - Desktop application framework
- **Svelte** - Frontend framework
- **Vite** - Build tool and development server
- **Bulma CSS** - CSS framework for styling
- **better-sqlite3** - Local SQLite database
- **svelte-french-toast** - Toast notifications
- **svelte-spa-router** - Client-side routing
- **RabbitMQ (amqplib)** - Message queue system
- **dotenv** - Environment variable management
- **Yarn** - Package manager

## Directory Structure

```
sevltefurpa/
├── data/                     # Local database files
│   └── furpa.db              # SQLite database file
├── src/                      # Source code
│   ├── main/                 # Electron main process
│   │   ├── ipc/              # IPC handlers for main-renderer communication
│   │   ├── rabbitmq/         # RabbitMQ integration
│   │   │   ├── incoming/     # Handlers for incoming RabbitMQ messages
│   │   │   ├── outgoing/     # Publishers for outgoing RabbitMQ messages
│   │   │   └── utils/        # RabbitMQ utility functions
│   │   ├── scripts/          # Utility scripts
│   │   ├── auth.js           # Authentication logic
│   │   ├── config.js         # Application configuration
│   │   ├── database.js       # Database operations
│   │   ├── main.js           # Main Electron process entry point
│   │   ├── preload.js        # Preload script for secure IPC
│   │   ├── printerService.js # Receipt printer integration
│   │   ├── rabbitmq.js       # RabbitMQ adapter
│   │   └── schema.js         # Database schema definition
│   └── renderer/             # Electron renderer process (Svelte frontend)
│       ├── components/       # Reusable Svelte components
│       ├── pages/            # Svelte page components
│       ├── public/           # Static assets
│       │   └── audio/        # Audio files for notifications
│       ├── stores/           # Svelte stores for state management
│       ├── utils/            # Utility functions
│       ├── App.svelte        # Main Svelte component
│       ├── index.html        # HTML entry point
│       └── main.js           # Renderer process entry point
├── .editorconfig             # Editor configuration
├── .env.example              # Example environment variables
├── .gitignore                # Git ignore file
├── .prettierignore           # Prettier ignore file
├── .prettierrc.cjs           # Prettier configuration
├── eslint.config.js          # ESLint configuration
├── package.json              # Project dependencies and scripts
├── svelte.config.js          # Svelte configuration
├── vite.config.mjs           # Vite configuration
└── various .md files         # Documentation and issue tracking
```

## Key Components

### Main Process (Electron)

The main process is responsible for:

- Creating and managing application windows
- Handling IPC communication with the renderer process
- Managing database connections
- Handling RabbitMQ connections and message processing
- Authentication and security

Key files:

- `src/main/main.js` - Entry point for the Electron application
- `src/main/database.js` - Database operations
- `src/main/rabbitmq.js` - RabbitMQ adapter
- `src/main/preload.js` - Secure IPC bridge

### Renderer Process (Svelte)

The renderer process handles:

- User interface rendering
- User interactions
- Client-side routing
- State management

Key files:

- `src/renderer/App.svelte` - Main Svelte component
- `src/renderer/main.js` - Renderer process entry point
- `src/renderer/pages/*.svelte` - Page components
- `src/renderer/stores/authStore.js` - Authentication state management

### IPC Communication

Communication between the main and renderer processes is handled through IPC (Inter-Process Communication):

- `src/main/ipc/index.js` - Central IPC handler setup
- `src/main/ipc/*.js` - Individual IPC handler modules
- `src/main/preload.js` - Exposes secure IPC channels to renderer

### RabbitMQ Integration

RabbitMQ is used for real-time data synchronization:

- `src/main/rabbitmq/channelPool.js` - Manages RabbitMQ channels
- `src/main/rabbitmq/consumer.js` - Consumes messages from RabbitMQ
- `src/main/rabbitmq/publisher.js` - Publishes messages to RabbitMQ
- `src/main/rabbitmq/incoming/` - Handlers for different types of incoming messages
- `src/main/rabbitmq/outgoing/` - Publishers for different types of outgoing messages

### Database

The application uses SQLite for local data storage:

- `src/main/database.js` - Database operations
- `src/main/schema.js` - Database schema definition
- `data/furpa.db` - SQLite database file

## Feature Documentation

## Application Flow

1. **Startup**:

   - Electron main process starts (`main.js`)
   - Database connection is established
   - RabbitMQ connection is established (if configured)
   - IPC handlers are set up
   - Main window is created and loaded with the renderer process

2. **Authentication**:

   - User is presented with login screen
   - Credentials are verified against the database
   - On successful login, user is redirected to the home screen

3. **Sales Process**:

   - User searches for products in the inventory
   - Products are added to the current sale
   - Payment is processed (cash, credit card, or split payment)
   - Sale is completed and stored in the database
   - Sale data is synchronized via RabbitMQ (if connected)

4. **Data Synchronization**:
   - RabbitMQ is used for bidirectional data synchronization
   - Incoming messages update local database
   - Outgoing messages publish local changes to remote systems

## Development

### Key Scripts

- `yarn dev` - Start Vite development server
- `yarn electron:dev` - Start Electron with development server
- `yarn dist` - Build and package the application
- `yarn format` - Format code with Prettier
- `yarn lint` - Lint code with ESLint

### Environment Variables

Environment variables are managed through a `.env` file (see `.env.example` for required variables).

## Conclusion

SevlteFurpa is a comprehensive desktop POS application with a modern technology stack. The application is designed to work both online (with RabbitMQ synchronization) and offline (with local SQLite database), making it suitable for retail environments with potentially unstable internet connections.

write efficient code
make minimal changes
don't explain
don't babble
