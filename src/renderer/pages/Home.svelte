<script>
  import { onMount } from 'svelte'
  import { currentUser } from '../stores/authStore.js'
  import {
    addItemToInProgressSale,
    completeInProgressSale,
    createInProgressSale,
    searchInventory as dbSearchInventory,
    generateSalesNumber,
    getPOSTransactionSequence,
    getTodaysInProgressSale,
    saveSaleTransaction,
  } from '../utils/database.js'
  import { showError, showInfo, showSuccess } from '../utils/toastUtils.js'
  import './Home.css'
  // Component state
  let searchTerm = ''
  let isSearching = false
  let salesItems = []
  let searchInput
  let sequenceNumber = 1
  let showDLSProducts = false

  // Sales management state
  let currentSale = null
  let currentSaleUuid = null
  let isInProgressSale = false

  // ULTRA BASIT Autocomplete
  let searchResults = []
  let showDropdown = false
  let selectedIndex = -1
  let searchTimeout = null

  function updateCustomerScreen() {
    if (window.electronAPI && window.electronAPI.updateCustomerScreen) {
      window.electronAPI.updateCustomerScreen(salesItems)
    }

    try {
      const customerWindow = window.open('', 'CustomerScreen')
      if (customerWindow) {
        customerWindow.postMessage(
          {
            type: 'SALES_UPDATE',
            items: salesItems,
          },
          '*'
        )
      }
    } catch {
      // ignore
    }
  }

  $: if (salesItems) {
    updateCustomerScreen()
  }

  // Payment system variables
  let paymentAmount = '₺0,00'
  let rawPaymentAmount = 0
  let selectedPaymentMethod = ''

  // Partial payment system
  let partialPayments = [] // Array to store partial payments
  let totalPaidAmount = 0 // Total amount paid so far
  let remainingAmount = 0 // Remaining amount to be paid

  // Cash payment change screen
  let showCashChangeModal = false
  let showPaymentMethodModal = false // New modal for payment method selection
  let showMealCardModal = false // New modal for meal card selection
  let showPaymentProcessingModal = false // Payment processing modal
  let showQuantityModal = false // New modal for quantity input
  let quantityModalItem = null // Item for quantity modal
  let quantityInput = ''
  let cashReceived = 0
  let changeAmount = 0

  // UTILITY FUNCTIONS
  function formatTurkishCurrency(amount) {
    const numericAmount = parseFloat(amount) || 0
    return numericAmount.toLocaleString('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  }

  function formatQuantity(quantity) {
    return quantity.toLocaleString('tr-TR', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    })
  }

  function parseQuantityInput(input) {
    return String(input).replace(',', '.')
  }

  // CustomerScreen'i yeni pencerede aç
  async function openCustomerScreen() {
    try {
      // Electron uygulaması için IPC kullanarak yeni pencere aç
      if (window.electronAPI && window.electronAPI.openCustomerWindow) {
        console.log('🪟 Electron API ile customer window açılıyor...')
        const result = await window.electronAPI.openCustomerWindow()
        if (result.success) {
          console.log('✅ Customer window başarıyla açıldı')
        } else {
          console.error('❌ Customer window açılamadı:', result.message)
        }
      } else {
        // Fallback: Aynı pencerede yeni tab olarak aç
        console.log('Electron API bulunamadı, alternatif çözüm kullanılıyor')
        // URL'yi yeni tab'da aç (popup blocker'ı bypass eder)
        const customerUrl = `${window.location.origin}/#/customer`
        const newTab = window.open(customerUrl, '_blank')
        if (!newTab) {
          console.warn('Popup blocked, manuel olarak /customer adresine gidin')
        }
      }
    } catch (error) {
      console.error('❌ Customer window açma hatası:', error)
    }
  }

  function isDecimalUnit(unit) {
    const decimalUnits = ['kg', 'KG', 'Kg', 'kG']
    return decimalUnits.includes(unit)
  }

  function validateQuantityInput(input, unit) {
    if (isDecimalUnit(unit)) {
      const decimalPattern = /^\d+([,]\d{0,2})?$/
      return decimalPattern.test(input)
    } else {
      const integerPattern = /^\d+$/
      return integerPattern.test(input)
    }
  }

  function getQuantityStep(unit) {
    return isDecimalUnit(unit) ? 0.01 : 1
  }

  function getMinimumQuantity(unit) {
    return isDecimalUnit(unit) ? 0.01 : 1
  }

  function handleQuantityKeydown(event, unit) {
    const key = event.key
    const currentValue = event.target.value
    const controlKeys = [
      'Backspace',
      'Delete',
      'Tab',
      'Escape',
      'Enter',
      'ArrowLeft',
      'ArrowRight',
      'ArrowUp',
      'ArrowDown',
      'Home',
      'End',
    ]

    if (controlKeys.includes(key)) return
    if (event.ctrlKey && ['a', 'c', 'v', 'x'].includes(key.toLowerCase())) return

    if (isDecimalUnit(unit)) {
      if (/^\d$/.test(key)) return
      if (key === ',' && !currentValue.includes(',')) return
    } else {
      if (/^\d$/.test(key)) return
    }
    event.preventDefault()
  }

  function updatePaymentDisplay() {
    if (rawPaymentAmount === 0) {
      paymentAmount = '₺0,00'
    } else {
      paymentAmount = formatTurkishCurrency(rawPaymentAmount / 100)
    }
  }

  // TABS
  let activeTab = 'search'
  function changeTab(tabName) {
    activeTab = tabName
    // When switching to search tab, ensure search input has focus
    if (tabName === 'search') {
      setTimeout(() => {
        ensureSearchInputFocus()
      }, 50)
    }
  }

  // Turkish character normalization utility
  function normalizeTurkish(text) {
    if (!text) return ''
    return text
      .toLowerCase()
      .replace(/[İI]/g, 'i')
      .replace(/[Ğğ]/g, 'g')
      .replace(/[Üü]/g, 'u')
      .replace(/[Şş]/g, 's')
      .replace(/[Öö]/g, 'o')
      .replace(/[Çç]/g, 'c')
  }

  // ULTRA BASIT ARAMA
  function handleInputChange() {
    if (searchTimeout) clearTimeout(searchTimeout)

    if (searchTerm.length < 2) {
      closeDropdown()
      return
    }

    searchTimeout = setTimeout(() => {
      performSearch()
    }, 300)
  }

  async function performSearch() {
    if (searchTerm.length < 2) return

    const inputWasFocused = document.activeElement === searchInput
    isSearching = true

    try {
      const normalizedSearchTerm = normalizeTurkish(searchTerm)
      const results = await dbSearchInventory(normalizedSearchTerm)

      let filteredResults = results || []
      if (showDLSProducts) {
        filteredResults = filteredResults.filter(
          item => item.name && normalizeTurkish(item.name).includes('dls')
        )
      } else {
        filteredResults = filteredResults.filter(
          item => !item.name || !normalizeTurkish(item.name).includes('dls')
        )
      }

      searchResults = filteredResults.slice(0, 15)

      if (searchResults.length > 0) {
        openDropdown()
        if (inputWasFocused && searchInput) {
          setTimeout(() => {
            ensureSearchInputFocus()
          }, 0)
        }
      } else {
        closeDropdown()
        ensureSearchInputFocus()
      }
    } catch {
      closeDropdown()
      ensureSearchInputFocus()
    }
    isSearching = false
  }

  async function handleAddButtonClick() {
    if (searchTerm.trim()) {
      await performSearchAndAddFirst()
    } else {
      ensureSearchInputFocus()
    }
  }

  // Input blur olayını kontrol et - dropdown açıksa focus'u geri ver
  function handleInputBlur(_event) {
    // Eğer dropdown açık ve blur dropdown'dan kaynaklanıyorsa focus'u geri ver
    if (showDropdown) {
      setTimeout(() => {
        if (searchInput && !searchInput.matches(':focus')) {
          ensureSearchInputFocus()
        }
      }, 10)
    }
  }

  // DROPDOWN PORTAL SYSTEM - BODY'YE EKLER
  let dropdownElement = null
  let inputRect = null

  function openDropdown() {
    closeDropdown() // Önce varsa kapat

    if (!searchInput || searchResults.length === 0) return

    // Input focus'unu koru
    const wasInputFocused = document.activeElement === searchInput

    // Input pozisyonunu al
    inputRect = searchInput.getBoundingClientRect()

    // Dropdown element oluştur
    dropdownElement = document.createElement('div')
    dropdownElement.style.cssText = `
      position: fixed;
      top: ${inputRect.bottom + 4}px;
      left: ${inputRect.left}px;
      width: ${inputRect.width}px;
      background: white;
      border: 2px solid #1e3a8a;
      border-radius: 8px;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      z-index: 99999;
      max-height: 500px;
      overflow-y: auto;
    `

    // İçeriği ekle
    dropdownElement.innerHTML = searchResults
      .map(
        (item, index) => `
      <div class="dropdown-item" data-index="${index}" style="
        padding: 1rem;
        cursor: pointer;
        border-bottom: 1px solid #f1f5f9;
        ${index === selectedIndex ? 'background: #f8f9fb; border-left: 4px solid #1e3a8a;' : ''}
      ">
        <div style="font-weight: bold; margin-bottom: 0.5rem;">${item.name}</div>
        <div style="display: flex; gap: 0.5rem; font-size: 0.8rem;">
          <span style="background: #f1f5f9; padding: 0.2rem 0.5rem; border-radius: 4px;">Kod: ${item.inventory_code}</span>
          <span style="background: #1e3a8a; color: white; padding: 0.2rem 0.5rem; border-radius: 4px;">
            ${item.price.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' })}
          </span>
        </div>
      </div>
    `
      )
      .join('')

    // Click eventleri ekle - mousedown ile focus kaybını engelle
    dropdownElement.querySelectorAll('.dropdown-item').forEach((el, index) => {
      // Mouse down'da focus kaybını engelle
      el.addEventListener('mousedown', e => {
        e.preventDefault() // Input'un blur olmasını engelle
      })

      el.addEventListener('click', e => {
        e.preventDefault()
        selectItem(searchResults[index])
      })
    })

    // Body'ye ekle
    document.body.appendChild(dropdownElement)
    showDropdown = true
    selectedIndex = -1

    // Input focus'unu geri ver
    if (wasInputFocused) {
      setTimeout(() => {
        ensureSearchInputFocus()
      }, 0)
    }
  }

  function closeDropdown() {
    if (dropdownElement) {
      document.body.removeChild(dropdownElement)
      dropdownElement = null
    }
    showDropdown = false
    selectedIndex = -1
  }

  function updateDropdownSelection() {
    if (!dropdownElement) return

    dropdownElement.querySelectorAll('.dropdown-item').forEach((el, index) => {
      if (index === selectedIndex) {
        el.style.background = '#f8f9fb'
        el.style.borderLeft = '4px solid #1e3a8a'
        el.scrollIntoView({ block: 'nearest' })
      } else {
        el.style.background = 'white'
        el.style.borderLeft = 'none'
      }
    })
  }

  async function selectItem(item) {
    closeDropdown()

    // Check if this is a KG item and search term is numeric
    if (isDecimalUnit(item.unit) && /^\d{7,}$/.test(searchTerm.trim())) {
      openQuantityModal(item)
    } else {
      await addItemToSales(item)
    }

    searchTerm = ''
    ensureSearchInputFocus()
  }

  // KLAVYE NAVİGASYONU
  function handleKeyDown(event) {
    // ENTER tuşu için özel kontrol - dropdown açık olmasa da çalışsın
    if (event.key === 'Enter') {
      event.preventDefault()

      // Eğer dropdown açık ve sonuçlar varsa
      if (showDropdown && searchResults.length > 0) {
        if (selectedIndex >= 0) {
          selectItem(searchResults[selectedIndex])
        } else {
          selectItem(searchResults[0])
        }
      }
      // Eğer dropdown kapalı ama arama terimi varsa, arama yap ve ilk sonucu ekle
      else if (searchTerm.trim().length >= 2) {
        performSearchAndAddFirst()
      }
      return
    }

    // Diğer tuşlar için dropdown açık olmalı
    if (!showDropdown || searchResults.length === 0) return

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        selectedIndex = selectedIndex < searchResults.length - 1 ? selectedIndex + 1 : 0
        updateDropdownSelection()
        break
      case 'ArrowUp':
        event.preventDefault()
        selectedIndex = selectedIndex > 0 ? selectedIndex - 1 : searchResults.length - 1
        updateDropdownSelection()
        break
      case 'Escape':
        closeDropdown()
        break
    }
  }

  // ENTER ile arama yap ve ilk sonucu ekle
  async function performSearchAndAddFirst() {
    if (isSearching || !searchTerm.trim()) return

    isSearching = true
    try {
      let searchKey = searchTerm.trim()
      let isWeightBarcode = false

      // Check if it's a weight barcode (numeric and length >= 7)
      if (/^\d{7,}$/.test(searchKey)) {
        // Take first 7 digits for weight barcode search
        const barcodePrefix = searchKey.substring(0, 7)

        // Try to find product with the prefix first
        const prefixResults = await dbSearchInventory(barcodePrefix)
        if (prefixResults && prefixResults.length > 0) {
          const item = prefixResults[0]
          if (isDecimalUnit(item.unit)) {
            // This is likely a weight barcode for a KG item
            isWeightBarcode = true
            searchKey = barcodePrefix
          }
        }
      }

      const normalizedSearchTerm = normalizeTurkish(searchKey)
      const results = await dbSearchInventory(normalizedSearchTerm)

      let filteredResults = results || []
      if (showDLSProducts) {
        filteredResults = filteredResults.filter(
          item => item.name && normalizeTurkish(item.name).includes('dls')
        )
      } else {
        filteredResults = filteredResults.filter(
          item => !item.name || !normalizeTurkish(item.name).includes('dls')
        )
      }

      if (filteredResults.length > 0) {
        const item = filteredResults[0]

        // If it's a weight barcode or KG item with numeric search, open quantity modal
        if ((isWeightBarcode || isDecimalUnit(item.unit)) && /^\d+$/.test(searchTerm.trim())) {
          openQuantityModal(item)
        } else {
          await addItemToSales(item)
          searchTerm = ''
          ensureSearchInputFocus()
        }
      } else {
        showError('Ürün bulunamadı')
        ensureSearchInputFocus()
      }
    } catch {
      showError('Arama sırasında hata oluştu')
      ensureSearchInputFocus()
    } finally {
      isSearching = false
    }
  }

  // FOCUS MANAGEMENT
  function ensureSearchInputFocus() {
    if (searchInput && document.activeElement !== searchInput) {
      setTimeout(() => {
        searchInput.focus()
      }, 0)
    }
  }

  // QUANTITY MODAL FUNCTIONS
  function openQuantityModal(item) {
    quantityModalItem = item
    quantityInput = ''
    showQuantityModal = true
    searchTerm = ''

    // Focus on quantity input after modal opens
    setTimeout(() => {
      const quantityInputElement = document.getElementById('quantity-input')
      if (quantityInputElement) {
        quantityInputElement.focus()
      }
    }, 100)
  }

  function closeQuantityModal() {
    showQuantityModal = false
    quantityModalItem = null
    quantityInput = ''
    ensureSearchInputFocus()
  }

  async function confirmQuantity() {
    if (!quantityModalItem || !quantityInput) {
      showError('Lütfen geçerli bir miktar giriniz!')
      return
    }

    const normalizedQuantity = parseQuantityInput(quantityInput)
    const quantity = parseFloat(normalizedQuantity)

    if (isNaN(quantity) || quantity <= 0) {
      showError('Lütfen geçerli bir miktar giriniz!')
      return
    }

    // Add item with custom quantity
    await addItemToSalesWithQuantity(quantityModalItem, quantity)
    closeQuantityModal()
  }

  async function addItemToSalesWithQuantity(item, customQuantity) {
    if (!item || !item.id) {
      showError('Geçersiz ürün bilgisi!')
      return
    }

    // Check if this is the first item being added (empty sales list)
    if (salesItems.length === 0) {
      await createNewSaleRecord()
    }

    const itemPrice = parseFloat(item.price) || 0
    const salesItem = {
      sequenceNo: sequenceNumber++,
      id: item.id,
      name: item.name,
      unit: item.unit || 'adet',
      price: itemPrice,
      original_price: itemPrice,
      inventory_code: item.inventory_code,
      barcode: item.barcode || '',
      quantity: customQuantity,
      total: itemPrice * customQuantity,
    }

    salesItems = [...salesItems, salesItem]
    showSuccess(
      `${item.name} satış listesine eklendi (${formatQuantity(customQuantity)} ${item.unit})`
    )

    // Add item to database if in progress sale exists
    if (currentSaleUuid) {
      addItemToCurrentSale(item)
    }
  }

  function handlePageClick(event) {
    // If user clicks anywhere on the page and no specific input is focused,
    // return focus to search input (unless they clicked on payment area or modals)
    const target = event.target
    const isInputOrButton =
      target.tagName === 'INPUT' || target.tagName === 'BUTTON' || target.tagName === 'TEXTAREA'
    const isInPaymentArea = target.closest('.content-panel')
    const isInModal = target.closest('.modal')

    if (!isInputOrButton && !isInPaymentArea && !isInModal && !showDropdown) {
      setTimeout(() => {
        ensureSearchInputFocus()
      }, 10)
    }
  }

  function handleVirtualKeyboardSearch(event) {
    const { item } = event.detail
    if (item) {
      addItemToSales(item)
      ensureSearchInputFocus()
    }
  }

  // CLICK OUTSIDE TO CLOSE
  function handleClickOutside(event) {
    if (
      showDropdown &&
      searchInput &&
      !searchInput.contains(event.target) &&
      dropdownElement &&
      !dropdownElement.contains(event.target)
    ) {
      closeDropdown()
    }
  }

  // BARCODE SCANNER
  let barcodeBuffer = ''
  let barcodeTimeout = null

  function handleGlobalKeyDown(event) {
    const target = event.target
    const isInputField = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA'

    if (!isInputField && event.key.length === 1) {
      if (barcodeTimeout) clearTimeout(barcodeTimeout)
      barcodeBuffer += event.key
      barcodeTimeout = setTimeout(async () => {
        if (barcodeBuffer.length >= 3) {
          searchTerm = barcodeBuffer
          await performSearchAndAddFirst()
          ensureSearchInputFocus()
        }
        barcodeBuffer = ''
      }, 100)
    }
  }

  // SALES MANAGEMENT
  async function addItemToSales(item) {
    console.log('🛒 addItemToSales çağrıldı')
    console.log('📦 Eklenecek item:', item)
    console.log('📋 Mevcut salesItems:', salesItems)

    if (!item || !item.id) {
      showError('Geçersiz ürün bilgisi!')
      ensureSearchInputFocus()
      return
    }

    const existingItemIndex = salesItems.findIndex(
      salesItem => salesItem.id === item.id || salesItem.inventory_code === item.inventory_code
    )

    console.log('🔍 existingItemIndex:', existingItemIndex)

    if (existingItemIndex !== -1) {
      console.log('♻️ Mevcut ürün bulundu, miktarı artırılıyor')
      incrementExistingItem(existingItemIndex, item)
    } else {
      console.log('🆕 Yeni ürün ekleniyor')
      await addNewItemToSales(item)
    }

    // Add item to database if in progress sale exists
    if (currentSaleUuid) {
      addItemToCurrentSale(item)
    }

    console.log('✅ İşlem sonrası salesItems:', salesItems)
    ensureSearchInputFocus()
  }

  function incrementExistingItem(existingItemIndex, item) {
    const updatedSalesItems = [...salesItems]
    const existingItem = updatedSalesItems[existingItemIndex]
    const increment = getQuantityStep(existingItem.unit)
    existingItem.quantity = Math.round((existingItem.quantity + increment) * 100) / 100
    existingItem.total = existingItem.quantity * existingItem.price
    salesItems = updatedSalesItems
    showSuccess(
      `${item.name} miktarı artırıldı (${formatQuantity(existingItem.quantity)} ${existingItem.unit})`
    )
    ensureSearchInputFocus()
  }

  async function addNewItemToSales(item) {
    // Check if this is the first item being added (empty sales list)
    if (salesItems.length === 0) {
      await createNewSaleRecord()
    }

    const initialQuantity = getMinimumQuantity(item.unit || 'adet')
    const itemPrice = parseFloat(item.price) || 0
    const salesItem = {
      sequenceNo: sequenceNumber++,
      id: item.id,
      name: item.name,
      unit: item.unit || 'adet',
      price: itemPrice,
      original_price: itemPrice,
      inventory_code: item.inventory_code,
      barcode: item.barcode || '',
      quantity: initialQuantity,
      total: itemPrice * initialQuantity,
    }
    salesItems = [...salesItems, salesItem]
    showSuccess(`${item.name} satış listesine eklendi`)
  }

  function removeItem(index) {
    const itemToRemove = salesItems[index]
    salesItems = salesItems.filter((_, i) => i !== index)
    reorderSequenceNumbers(index)
    showSuccess(`${itemToRemove.name} satış listesinden kaldırıldı`)
    ensureSearchInputFocus()
  }

  function reorderSequenceNumbers(removedIndex) {
    if (removedIndex === 0) {
      salesItems = salesItems.map(item => ({ ...item, sequenceNo: item.sequenceNo - 1 }))
      sequenceNumber = sequenceNumber - 1
    } else {
      salesItems = salesItems.map(item => ({
        ...item,
        sequenceNo: item.sequenceNo > removedIndex + 1 ? item.sequenceNo - 1 : item.sequenceNo,
      }))
      sequenceNumber = sequenceNumber - 1
    }
  }

  function increaseQuantity(index) {
    const updatedSalesItems = [...salesItems]
    const item = updatedSalesItems[index]
    const increment = getQuantityStep(item.unit)
    item.quantity = Math.round((item.quantity + increment) * 100) / 100
    item.total = item.quantity * item.price
    salesItems = updatedSalesItems
    showSuccess(`${item.name} miktarı artırıldı (${formatQuantity(item.quantity)} ${item.unit})`)
    ensureSearchInputFocus()
  }

  function decreaseQuantity(index) {
    const updatedSalesItems = [...salesItems]
    const item = updatedSalesItems[index]
    const decrement = getQuantityStep(item.unit)
    const minQuantity = getMinimumQuantity(item.unit)
    const newQuantity = Math.round((item.quantity - decrement) * 100) / 100

    if (newQuantity >= minQuantity) {
      item.quantity = newQuantity
      item.total = item.quantity * item.price
      salesItems = updatedSalesItems
      showSuccess(`${item.name} miktarı azaltıldı (${formatQuantity(item.quantity)} ${item.unit})`)
      ensureSearchInputFocus()
    } else {
      removeItem(index)
    }
  }

  function handleQuantityChange(index, newQuantity) {
    const updatedSalesItems = [...salesItems]
    const item = updatedSalesItems[index]

    if (!validateQuantityInput(newQuantity, item.unit)) {
      showError(
        `${item.unit} birimi için ${isDecimalUnit(item.unit) ? 'ondalık değer (örn: 1,50)' : 'tam sayı'} giriniz`
      )
      ensureSearchInputFocus()
      return
    }

    const normalizedQuantity = parseQuantityInput(newQuantity)
    let quantity = parseFloat(normalizedQuantity)
    const minQuantity = getMinimumQuantity(item.unit)
    if (isNaN(quantity) || quantity <= 0) quantity = minQuantity
    quantity = Math.round(quantity * 100) / 100

    if (quantity < minQuantity) {
      removeItem(index)
      return
    }

    item.quantity = quantity
    item.total = item.quantity * item.price
    salesItems = updatedSalesItems
    ensureSearchInputFocus()
  }

  $: totalAmount = salesItems.reduce((sum, item) => sum + item.total, 0)
  $: remainingAmount = totalAmount - totalPaidAmount
  $: isPaymentComplete = remainingAmount <= 0 && totalAmount > 0

  // Reactive statement to maintain focus when switching to search tab
  $: if (activeTab === 'search' && searchInput) {
    setTimeout(() => {
      ensureSearchInputFocus()
    }, 10)
  }

  // PAYMENT METHODS
  function addToAmount(digit) {
    rawPaymentAmount = rawPaymentAmount * 10 + (digit === '.' ? 0 : parseInt(digit))
    updatePaymentDisplay()
  }

  function removeLastDigit() {
    rawPaymentAmount = Math.floor(rawPaymentAmount / 10)
    updatePaymentDisplay()
  }

  function clearAmount() {
    rawPaymentAmount = 0
    updatePaymentDisplay()
  }

  function closeCashChangeModal() {
    showCashChangeModal = false
    cashReceived = 0
    changeAmount = 0
  }

  // Payment Method Modal Functions
  function openPaymentMethodModal() {
    showPaymentMethodModal = true
  }

  function closePaymentMethodModal() {
    showPaymentMethodModal = false
  }

  // Meal Card Modal Functions
  function openMealCardModal() {
    showMealCardModal = true
  }

  function closeMealCardModal() {
    showMealCardModal = false
  }

  function selectMealCardType(cardType) {
    closeMealCardModal()

    // Get the amount from keypad (convert from cents to TL)
    const keypadAmount = rawPaymentAmount / 100

    // If no keypad amount, use remaining amount
    const paymentAmount =
      keypadAmount > 0 && keypadAmount <= remainingAmount ? keypadAmount : remainingAmount

    // Add meal card payment with specific type to the split payments list
    const success = addPartialPayment('meal-card', paymentAmount, cardType)

    if (success) {
      // Clear keypad and reset UI
      clearPaymentInput()

      // Check if payment is now complete
      if (isPaymentComplete) {
        showSuccess('Yemek kartı ödemesi tamamlandı!')
      } else {
        showSuccess(`${paymentAmount.toFixed(2)} TL ${cardType} ödemesi eklendi`)
        showInfo(`Kalan tutar: ${remainingAmount.toFixed(2)} TL`)
      }
    }
  }

  function selectPaymentMethodFromModal(method) {
    closePaymentMethodModal()

    if (method === 'cash') {
      // For cash, open the cash change modal with remaining amount
      cashReceived = remainingAmount
      changeAmount = 0
      showCashChangeModal = true
    } else if (method === 'split') {
      // For split payment, show info that user can now select individual payment methods
      showInfo('Bölünmüş ödeme seçildi. Ödeme yöntemlerini seçerek tutarları ekleyebilirsiniz.')
    } else if (method === 'credit-card') {
      // Kredi kartı ödemesini sadece listeye ekle, POS'a gönderme
      handleCreditCardPaymentOnly(remainingAmount)
    }
  }

  async function completeCashSale() {
    console.log('💵 Nakit satış tamamlanıyor:', {
      cashReceived,
      remainingAmount,
      changeAmount,
    })

    if (cashReceived < remainingAmount) {
      showError('Alınan nakit tutar yetersiz!')
      return
    }

    const actualPayment = Math.min(cashReceived, remainingAmount)
    const success = addPartialPayment('cash', actualPayment)

    if (success) {
      selectedPaymentMethod = ''
      rawPaymentAmount = 0
      updatePaymentDisplay()
      document.querySelectorAll('.payment-btn').forEach(btn => {
        btn.classList.remove('is-active', 'is-selected')
      })

      if (changeAmount > 0) {
        showSuccess(
          `Nakit ödeme tamamlandı! Para üstü: ${changeAmount.toLocaleString('tr-TR', {
            style: 'currency',
            currency: 'TRY',
          })}`
        )
      } else {
        showSuccess('Nakit ödeme tamamlandı!')
      }

      closeCashChangeModal()

      // Check if payment is complete and complete sale
      if (isPaymentComplete) {
        await completeSale()
      }
    }
  }

  // Enhanced function for completing cash sales with change calculation
  async function completeCashSaleWithChange() {
    console.log('💵 Para üstü ile nakit satış tamamlanıyor:', {
      cashReceived,
      remainingAmount,
      changeAmount,
    })

    // Add the exact remaining amount as payment (not the full cash received)
    const success = addPartialPayment('cash', remainingAmount)

    if (success) {
      // Close the modal
      showCashChangeModal = false

      // Show change message with enhanced formatting
      showSuccess(`💰 Para üstü: ${changeAmount.toFixed(2)} TL verilecek`)

      // Complete the sale
      await completeSale()
    }
  }

  function selectPaymentMethod(method) {
    // Check if there are items in the cart
    if (salesItems.length === 0) {
      showError('Satış listesi boş!')
      return
    }

    // Check if payment is already complete
    if (remainingAmount <= 0) {
      showError('Ödeme zaten tamamlanmış!')
      return
    }

    // Set the payment method as active in UI
    selectedPaymentMethod = method
    document.querySelectorAll('.payment-btn').forEach(btn => {
      btn.classList.remove('is-active', 'is-selected')
    })
    const selectedButton = document.querySelector(
      `.payment-btn.${method.toLowerCase().replace(/\s+/g, '-')}`
    )
    if (selectedButton) {
      selectedButton.classList.add('is-active', 'is-selected')
    }

    // Get the amount from keypad (convert from cents to TL)
    const keypadAmount = rawPaymentAmount / 100

    // If no keypad amount, show instruction message
    if (keypadAmount <= 0) {
      showInfo(`${getPaymentMethodDisplayName(method)} seçildi. Ödeme tutarını girin.`)
      return
    }

    // Process the payment based on method
    if (method === 'cash') {
      handleCashPaymentWorkflow(keypadAmount)
    } else if (method === 'credit-card') {
      // Validate payment amount doesn't exceed remaining amount for credit card
      if (keypadAmount > remainingAmount) {
        showError(`Ödeme tutarı kalan tutardan (${remainingAmount.toFixed(2)} TL) fazla olamaz!`)
        return
      }
      // Kredi kartı ödemelerini sadece listeye ekle, POS'a gönderme
      handleCreditCardPaymentOnly(keypadAmount)
    } else if (method === 'meal-card') {
      // Validate payment amount doesn't exceed remaining amount for meal card
      if (keypadAmount > remainingAmount) {
        showError(`Ödeme tutarı kalan tutardan (${remainingAmount.toFixed(2)} TL) fazla olamaz!`)
        return
      }
      // Open meal card modal to select Multinet or Sodexo
      openMealCardModal()
    }
  }

  // ENHANCED CASH PAYMENT WORKFLOW WITH CHANGE MODAL
  function handleCashPaymentWorkflow(amount) {
    console.log('💵 Nakit ödeme workflow:', { amount, remainingAmount })

    // Validate payment amount
    if (amount <= 0) {
      showError("Ödeme tutarı 0'dan büyük olmalıdır!")
      return
    }

    // Check if cash payment exceeds remaining amount (requires change calculation)
    if (amount > remainingAmount) {
      // Show change calculation modal
      cashReceived = amount
      changeAmount = amount - remainingAmount
      showCashChangeModal = true
      console.log('💰 Para üstü modalı açılıyor:', {
        cashReceived,
        remainingAmount,
        changeAmount,
      })
    } else {
      // Direct payment without change
      const success = addPartialPayment('cash', amount)

      if (success) {
        clearPaymentInput()
        showSuccess(`${amount.toFixed(2)} TL nakit ödeme eklendi`)

        // Check if payment is complete and complete sale
        if (isPaymentComplete) {
          setTimeout(async () => {
            await completeSale()
          }, 500)
        } else {
          showInfo(`Kalan tutar: ${remainingAmount.toFixed(2)} TL`)
        }
      }
    }
  }

  // Function for credit card payment without immediate POS integration (for split payments)
  async function handleCreditCardPaymentOnly(amount) {
    console.log('💳 Kredi kartı ödeme (sadece liste):', { amount, remainingAmount })

    // Validate payment amount
    if (amount <= 0) {
      showError("Ödeme tutarı 0'dan büyük olmalıdır!")
      return
    }

    if (amount > remainingAmount) {
      showError(`Ödeme tutarı kalan tutardan (${remainingAmount.toFixed(2)} TL) fazla olamaz!`)
      return
    }

    // Add credit card payment to the split payments list without POS integration
    const success = addPartialPayment('credit-card', amount)

    if (success) {
      // Clear keypad and reset UI
      clearPaymentInput()

      // Check if payment is now complete
      if (isPaymentComplete) {
        showSuccess('Ödeme tamamlandı! "Satışı Tamamla" butonuna tıklayın.')
      } else {
        showSuccess(`${amount.toFixed(2)} TL kredi kartı ödemesi eklendi`)
        showInfo(`Kalan tutar: ${remainingAmount.toFixed(2)} TL`)
      }
    }
  }

  function clearPaymentInput() {
    rawPaymentAmount = 0
    updatePaymentDisplay()
    selectedPaymentMethod = ''

    // Clear payment button selections
    document.querySelectorAll('.payment-btn').forEach(btn => {
      btn.classList.remove('is-active', 'is-selected')
    })
  }

  // ENHANCED SPLIT PAYMENT FUNCTIONS
  function addPartialPayment(method, amount, cardType = null) {
    // Enhanced validation for payment addition
    if (amount <= 0) {
      showError("Ödeme tutarı 0'dan büyük olmalıdır!")
      return false
    }

    if (amount > remainingAmount) {
      showError(`Kalan tutardan (${remainingAmount.toFixed(2)} TL) fazla ödeme yapılamaz!`)
      return false
    }

    // Check if adding this payment would exceed total amount with tolerance
    if (totalPaidAmount + amount > totalAmount + 0.01) {
      showError(`Bu ödeme toplam tutarı (${totalAmount.toFixed(2)} TL) aşacaktır!`)
      return false
    }

    // Validate payment method
    const validMethods = ['cash', 'credit-card', 'meal-card']
    if (!validMethods.includes(method)) {
      showError('Geçersiz ödeme yöntemi!')
      return false
    }

    // Round amount to prevent floating point issues
    const roundedAmount = Math.round(amount * 100) / 100

    const payment = {
      id: Date.now() + Math.random(), // Ensure unique ID
      method,
      amount: roundedAmount,
      timestamp: new Date().toISOString(),
      methodDisplayName: getPaymentMethodDisplayName(method, cardType),
      cardType, // Store the specific card type for meal cards
    }

    partialPayments = [...partialPayments, payment]
    totalPaidAmount += amount

    console.log('💰 Kısmi ödeme eklendi:', {
      payment,
      totalPaid: totalPaidAmount,
      remaining: remainingAmount,
      isComplete: isPaymentComplete,
    })

    // Clear the payment input
    rawPaymentAmount = 0
    updatePaymentDisplay()

    return true
  }

  function removePartialPayment(paymentId) {
    const payment = partialPayments.find(p => p.id === paymentId)
    if (payment) {
      partialPayments = partialPayments.filter(p => p.id !== paymentId)
      totalPaidAmount -= payment.amount
      showSuccess(`${payment.amount.toFixed(2)} TL ${payment.methodDisplayName} ödemesi kaldırıldı`)
    }
  }

  function getPaymentMethodDisplayName(method, cardType = null) {
    const methodNames = {
      cash: 'Nakit',
      'credit-card': 'Kredi Kartı',
      'meal-card': cardType ? cardType : 'Yemek Kartı',
    }
    return methodNames[method] || method
  }

  // Enhanced payment validation function
  function validatePayments() {
    // Check if there are any items in the cart
    if (salesItems.length === 0) {
      return {
        valid: false,
        message: 'Satış listesi boş! Lütfen önce ürün ekleyin.',
        type: 'warning',
      }
    }

    // Check if there are any payments
    if (partialPayments.length === 0) {
      return {
        valid: false,
        message: 'Hiç ödeme yapılmamış! Lütfen ödeme yöntemi seçin.',
        type: 'info',
      }
    }

    // Calculate totals with high precision
    const totalPaid = partialPayments.reduce((sum, payment) => sum + payment.amount, 0)
    const difference = totalAmount - totalPaid

    // Check for negative amounts
    const invalidPayments = partialPayments.filter(p => p.amount <= 0)
    if (invalidPayments.length > 0) {
      return {
        valid: false,
        message: "Geçersiz ödeme tutarları bulundu! Tüm ödemeler 0'dan büyük olmalıdır.",
        type: 'error',
      }
    }

    // Check if payments are less than transaction amount (with tolerance)
    if (difference > 0.01) {
      return {
        valid: false,
        message: `Ödeme eksik! Kalan tutar: ${difference.toFixed(2)} TL. Toplam: ${totalAmount.toFixed(2)} TL, Ödenen: ${totalPaid.toFixed(2)} TL`,
        type: 'warning',
      }
    }

    // Check if payments exceed transaction amount (with tolerance)
    if (difference < -0.01) {
      return {
        valid: false,
        message: `Ödeme fazla! Fazla tutar: ${Math.abs(difference).toFixed(2)} TL. Toplam: ${totalAmount.toFixed(2)} TL, Ödenen: ${totalPaid.toFixed(2)} TL`,
        type: 'error',
      }
    }

    // Check for duplicate payment IDs (data integrity)
    const paymentIds = partialPayments.map(p => p.id)
    const uniqueIds = new Set(paymentIds)
    if (paymentIds.length !== uniqueIds.size) {
      return {
        valid: false,
        message: 'Ödeme verilerinde tutarsızlık bulundu! Lütfen sayfayı yenileyin.',
        type: 'error',
      }
    }

    // All validations passed
    return {
      valid: true,
      message: `Ödeme doğrulandı! Toplam: ${totalAmount.toFixed(2)} TL, Ödenen: ${totalPaid.toFixed(2)} TL`,
      type: 'success',
    }
  }

  async function makeRequest(deviceIp, path, method, payload, retryCount = 0) {
    const MAX_RETRIES = 3

    console.log(`[POS] Request attempt ${retryCount + 1}/${MAX_RETRIES + 1}`)

    if (retryCount >= MAX_RETRIES) {
      throw new Error('İstek tekrar denemesi başarısız oldu. Lütfen işlemi tekrar deneyiniz.')
    }

    try {
      console.log(`[POS] Making ${method} request to: https://${deviceIp}:4567/${path}`)
      console.log('[POS] Payload:', JSON.stringify(payload, null, 2))

      const response = await window.electronAPI.https.request({
        hostname: deviceIp,
        port: 4567,
        path,
        method,
        payload,
      })

      console.log('[POS] Response:', response)

      if (response.HasError) {
        console.error('[PAVO] Error response:', response)
        console.log('[PAVO] Error Code:', response.ErrorCode, 'Retry Count:', retryCount)

        if (response.ErrorCode === 73) {
          console.log(
            '[PAVO] Sequence error, retrying with new sequence:',
            response.TransactionHandle.TransactionSequence
          )

          // Update global sequence number
          sequenceNumber = getPOSTransactionSequence() //response.TransactionHandle.TransactionSequence
          console.log('📊 Global sequenceNumber updated to:', sequenceNumber)

          // Update payload with new sequence
          if (payload.TransactionHandle) {
            payload.TransactionHandle.TransactionSequence =
              response.TransactionHandle.TransactionSequence
          }

          // Retry the request
          return await makeRequest(deviceIp, path, method, payload, retryCount + 1)
        } else if (response.ErrorCode === 72) {
          console.log(
            '[PAVO] Time sync error, retrying with POS time:',
            response.TransactionHandle.TransactionDate
          )
          console.log('[PAVO] Original time was:', payload.Header?.DateTime)
          console.log(
            '[PAVO] Original sequence was:',
            payload.TransactionHandle?.TransactionSequence
          )

          // Update global sequence number from time sync error too
          if (response.TransactionHandle && response.TransactionHandle.TransactionSequence) {
            sequenceNumber = response.TransactionHandle.TransactionSequence
            console.log('📊 Global sequenceNumber updated from time sync:', sequenceNumber)
          }

          // Update payload with POS time and sequence
          if (payload.TransactionHandle) {
            payload.TransactionHandle.TransactionSequence =
              response.TransactionHandle.TransactionSequence
          }

          if (payload.Header) {
            payload.Header.DateTime = response.TransactionHandle.TransactionDate
          }

          if (payload.Transaction) {
            payload.Transaction.TransactionDate = response.TransactionHandle.TransactionDate
          }

          console.log('[PAVO] Updated payload for retry:', {
            sequence: payload.TransactionHandle?.TransactionSequence,
            headerTime: payload.Header?.DateTime,
            transactionTime: payload.Transaction?.TransactionDate,
          })

          // Retry the request
          console.log('[PAVO] Starting retry with corrected time...')
          return await makeRequest(deviceIp, path, method, payload, retryCount + 1)
        } else {
          console.error('PAVO MakeRequest error:', response)
          throw new Error(
            response.Message || 'İstek gönderildi, ama PAVO bilinmeyen bir hata dönüşü yaptı'
          )
        }
      }

      // Başarılı response'larda da sequence güncelle
      if (
        response &&
        response.TransactionHandle &&
        response.TransactionHandle.TransactionSequence
      ) {
        const newSequence = response.TransactionHandle.TransactionSequence
        if (newSequence !== sequenceNumber) {
          console.log("📊 Başarılı response'tan sequence güncelleniyor:", {
            old: sequenceNumber,
            new: newSequence,
            path,
          })
          sequenceNumber = newSequence
        }
      }

      return response
    } catch (error) {
      console.error('İstek gönderildi, ama cihazın yanıtı alımı sırasında hata oluştu', error)
      throw new Error('İstek gönderildi, ama cihazın yanıtı alımı sırasında hata oluştu')
    }
  }

  /**
   * Demo satış verisi gönderme fonksiyonu (test amaçlı)
   */

  /**
   * Pairing function using https module
   */
  // Bu değişkeni global veya store'da tutıyorsanız kaldırabilirsiniz,
  // artık localStorage'dan yöneteceğiz.
  // let sequenceNumber;

  // Bu değişkeni global veya store'da tutuyorsanız kaldırabilirsiniz,
  // artık localStorage'dan yöneteceğiz.
  // let sequenceNumber;

  /**
   * Cihazı POS ile eşleştirir.
   * TransactionSequence numarasını localStorage'dan okur, API'ye gönderir.
   * Başarılı yanıttan sonra, gelen sequence'ın bir fazlasını bir sonraki işlem için localStorage'a kaydeder.
   * @param {object} deviceConfig - Cihaz yapılandırması (ipAddress, serialNumber, name)
   * @returns {Promise<object>} - İşlem sonucunu ve cihaz bilgilerini içeren nesne.
   */
  /**
   * Cihazı POS ile eşleştirir ve TransactionSequence'ı yönetir.
   * Bu fonksiyon, bir işlem öncesi çağrılarak hem bağlantıyı teyit eder hem de
   * kullanılacak doğru TransactionSequence numarasını alıp, bir sonrakini hazırlar.
   * @param {object} deviceConfig - Cihaz yapılandırması (ipAddress, serialNumber, name)
   * @returns {Promise<object>} - İşlem sonucunu ve bu işlemde kullanılacak sequence'ı içeren nesne.
   */
  async function pairDevice(deviceConfig) {
    console.log('[PAIR_DEVICE] Cihaz eşleştirme ve sequence senkronizasyonu başlatılıyor...')

    const currentSequenceStr = await getPOSTransactionSequence()
    // localStorage boşsa veya geçersizse, varsayılan olarak 1 kullan. 0 genellikle geçersiz bir başlangıçtır.

    const now = new Date()
    now.setHours(now.getHours() + 3)
    const transactionDate = now.toISOString().split('Z')[0]

    try {
      const transactionHandle = {
        TransactionHandle: {
          SerialNumber: deviceConfig.serialNumber,
          TransactionDate: transactionDate,
          TransactionSequence: currentSequenceStr, // Bu işlem için okunan değeri kullan
          Fingerprint: 'shopigo',
        },
      }

      console.log('[PAIR_DEVICE] Pairing payload:', JSON.stringify(transactionHandle, null, 2))

      const response = await makeRequest(
        deviceConfig.ipAddress,
        'Pairing',
        'POST',
        transactionHandle
      )

      console.log(
        '[PAIR_DEVICE] Cihaz başarıyla eşleştirildi (veya sequence senkronize edildi):',
        response
      )

      return {
        success: true,
        message: 'Device paired and sequence synchronized successfully',
        device: {
          // Geriye bu işlemde BAŞARIYLA KULLANILAN sequence numarasını döndür.
          transactionSequence: currentSequenceStr,
        },
      }
    } catch (error) {
      console.error('[PAIR_DEVICE] Eşleştirme/Senkronizasyon hatası:', error)
      return {
        success: false,
        message: error.message || 'Pairing/sync failed',
        device: null,
      }
    }
  }

  /**
   * Satış verilerini POS cihazına gönderir.
   * Göndermeden önce pairDevice'ı çağırarak güncel TransactionSequence numarasını alır.
   */
  async function sendRealSaleData() {
    if (salesItems.length === 0) {
      throw new Error('Satış listesi boş!')
    }

    // Show payment processing modal
    showPaymentProcessingModal = true

    try {
      const deviceConfig = {
        serialNumber: 'PAV600000327',
        ipAddress: '************',
        name: 'shopigo',
      }
      // --- DEĞİŞİKLİK 1: `pairDevice`'ı `await` ile çağır ve sonucunu bekle ---
      console.log('[SALE_DATA] Satış işlemi için güncel sequence numarası alınıyor...')
      await pairDevice(deviceConfig)

      const sequenceForSale = await getPOSTransactionSequence()
      console.log(
        `[SALE_DATA] Satış işlemi için kullanılacak TransactionSequence: ${sequenceForSale}`
      )

      // =========================================================================================
      // BU KISIMDAN SONRASI SİZİN MEVCUT HESAPLAMA KODUNUZ, DEĞİŞTİRMEYE GEREK YOK
      // =========================================================================================

      // Calculate dynamic payment amount based on keypad and remaining amount
      const keypadAmount = rawPaymentAmount / 100 // Convert from cents to TL
      const dynamicPaymentAmount =
        keypadAmount > 0 && keypadAmount <= remainingAmount ? keypadAmount : remainingAmount

      // Generate unique order number
      const orderNo = `ORDER-${Date.now()}`

      // Generate the current date and time in Turkey timezone (UTC+3)
      const now = new Date()
      now.setHours(now.getHours() + 3)
      const transactionDate = now.toISOString().split('Z')[0]

      // ... (Burada sizin ürün, ödeme vb. tüm hesaplamalarınız yer alıyor)
      // Sizin kodunuzdan kopyalanan kısımlar:
      const addedSaleItems = salesItems.map((item, index) => {
        const quantity = parseFloat(item.quantity || 1)
        const total = parseFloat(item.total || 0)
        let unitPrice = parseFloat(item.price || 0)
        if (unitPrice <= 0 && total > 0 && quantity > 0) {
          unitPrice = total / quantity
        }
        if (unitPrice <= 0) {
          unitPrice = 0.01
        }
        unitPrice = Math.round(unitPrice * 100) / 100
        const calculatedTotal = Math.round(quantity * unitPrice * 100) / 100
        const useStrategy1 = true
        const grossPrice = useStrategy1 ? calculatedTotal : unitPrice
        return {
          Name: item.name || `Ürün ${index + 1}`,
          IsGeneric: true,
          UnitCode: 'C62',
          TaxGroupCode: 'KDV18',
          ItemQuantity: quantity,
          UnitPriceAmount: unitPrice,
          GrossPriceAmount: grossPrice,
          TotalPriceAmount: calculatedTotal,
          ReservedText: item.inventory_code || item.barcode || item.id || `ITEM-${index + 1}`,
        }
      })
      const calculatedTotalAmount = addedSaleItems.reduce(
        (sum, item) => sum + item.TotalPriceAmount,
        0
      )
      const isPartialPayment = dynamicPaymentAmount < calculatedTotalAmount
      const scaledSaleItems = addedSaleItems.map(item => {
        if (!isPartialPayment) return item
        const scaledUnitPrice = item.UnitPriceAmount
        const scaledTotalPrice = item.ItemQuantity * scaledUnitPrice
        const scaledGrossPrice = scaledTotalPrice
        return {
          ...item,
          UnitPriceAmount: scaledUnitPrice,
          GrossPriceAmount: scaledGrossPrice,
          TotalPriceAmount: scaledTotalPrice,
        }
      })
      const paymentInformations = []
      const hasSplitPayments = partialPayments.length > 1
      if (hasSplitPayments) {
        const mediatorMap = { cash: 1, 'credit-card': 2, 'meal-card': 4 }
        partialPayments.forEach(payment => {
          paymentInformations.push({
            Mediator: mediatorMap[payment.method] || 2,
            Amount: payment.amount,
            CurrencyCode: 'TRY',
            ExchangeRate: 1,
            ExternalReferenceText: `ref-${payment.id}`,
          })
        })
      } else {
        const mediatorMap = { cash: 1, 'credit-card': 2, 'meal-card': 4 }
        if (partialPayments.length > 0) {
          const payment = partialPayments[0]
          paymentInformations.push({
            Mediator: mediatorMap[payment.method] || 2,
            Amount: payment.amount,
            CurrencyCode: 'TRY',
            ExchangeRate: 1,
            ExternalReferenceText: `ref-${payment.id}`,
          })
        }
      }
      const posTotal = hasSplitPayments
        ? partialPayments.reduce((sum, payment) => sum + payment.amount, 0)
        : partialPayments.length > 0
          ? partialPayments[0].amount
          : dynamicPaymentAmount

      console.log('💰 POS Total calculation:', {
        hasSplitPayments,
        partialPaymentsCount: partialPayments.length,
        dynamicPaymentAmount,
        firstPaymentAmount: partialPayments.length > 0 ? partialPayments[0].amount : null,
        calculatedPosTotal: posTotal,
        partialPayments: partialPayments.map(p => ({ method: p.method, amount: p.amount })),
      })

      // ... (hesaplamaların sonu)

      // =========================================================================================

      // TypeScript uyumlu payload yapısı
      const data = {
        TransactionHandle: {
          SerialNumber: 'PAV600000327', //deviceConfig.serialNumber, // deviceConfig'ten almak daha dinamik
          TransactionDate: transactionDate,
          // --- DEĞİŞİKLİK 4: Alınan doğru sequence'ı payload'a ekle ---
          TransactionSequence: sequenceForSale + 1,
          Fingerprint: 'shopigo',
        },
        Sale: {
          RefererApp: 'shopigo',
          RefererAppVersion: '1.0.0',
          OrderNo: orderNo,
          MainDocumentType: 1,
          GrossPrice: posTotal,
          TotalPrice: posTotal,
          CurrencyCode: 'TRY',
          ExchangeRate: 1,
          SendPhoneNotification: false,
          SendEMailNotification: false,
          NotificationPhone: '',
          DocumentNote: '',
          NotificationEMail: '<EMAIL>',
          ShowCreditCardMenu: false,
          SelectedSlots: ['rf', 'icc', 'manual', 'qr'],

          EnableAllTerminalsOnRetry: false,
          AllowDismissCardRead: false,
          CardReadTimeout: 30,
          SkipAmountCash: true,
          CancelPaymentLater: true,
          AskCustomer: false,
          SendResponseBeforePrint: false,
          TryAgainOnPaymentFailure: true,
          ReferOtherMediatorsToRetryPayment: true,
          AbandonOptions: { IsVoid: true, EnableRefundMediatorsOnVoidFailure: true },
          ContinuePaymentWithCardInserted: true,
          HeadUnmaskedCardNumber: 4,
          TailUnmaskedCardNumber: 4,
          ReceiptInformation: {
            ReceiptJsonEnabled: true,
            ReceiptTextEnabled: false,
            ReceiptImageEnabled: false,
            ReceiptWidth: '58mm',
            PrintCustomerReceipt: false,
            PrintMerchantReceipt: false,
            PrintCustomerReceiptCopy: false,
            EnableExchangeRateField: true,
          },
          AddedSaleItems: scaledSaleItems,
          PaymentInformations: paymentInformations,
          AllowedPaymentMediators: [{ Mediator: 2 }, { Mediator: 1 }, { Mediator: 4 }],
          CustomerParty: {
            CustomerType: 1,
            FirstName: 'Müşteri',
            MiddleName: 'demo',
            FamilyName: 'demo',
            CompanyName: 'demo',
            TaxOfficeCode: '',
            TaxNumber: '11111111111',
            Phone: '5417818194',
            EMail: '<EMAIL>',
            Country: 'Türkiye',
            City: 'Istanbul',
            District: '',
            Neighborhood: '',
            Address: '',
          },
          AdditionalInfo: [{ Key: 'ShopigoFurpa', Value: 'Gerçek Satış', Print: true }],
        },
      }

      try {
        console.log('📤 Gerçek satış verileri gönderiliyor...', {
          total: posTotal,
          sequence: sequenceForSale,
          posData: JSON.stringify(data, null, 2), // Payload'ın tamamını görmek için
        })
        const response = await makeRequest('************', 'CompleteSale', 'POST', data)

        // Satış başarılı. `pairDevice` zaten bir sonraki numara için localStorage'ı güncelledi.
        // Bu nedenle burada ek bir işlem yapmaya gerek yok.

        console.log('✅ Gerçek satış başarılı:', response)
        return response
      } catch (error) {
        console.error('❌ Gerçek satış hatası:', error)
        // Hata durumunda sequence artırılmadığı için, bir sonraki deneme aynı numarayla yapılabilir.
        throw error
      }
    } catch (error) {
      console.error('❌ Satış işlemi hatası:', error)
      // Show user-friendly error message
      showError('Ödeme başarısız! POS cihazı ile bağlantı kurulamadı veya işlem reddedildi.')
      throw error
    } finally {
      // Always close the payment processing modal
      showPaymentProcessingModal = false
    }
  }

  function payRemainingAmount() {
    if (remainingAmount <= 0) {
      showError('Kalan tutar bulunmuyor!')
      return
    }

    if (!selectedPaymentMethod) {
      showError('Lütfen ödeme yöntemi seçiniz!')
      return
    }

    console.log('💰 Kalan tutar ödeme:', {
      remainingAmount,
      selectedPaymentMethod,
    })

    // Use the new workflow functions to process the remaining amount
    if (selectedPaymentMethod === 'cash') {
      handleCashPaymentWorkflow(remainingAmount)
    } else if (selectedPaymentMethod === 'credit-card') {
      // Kredi kartı ödemelerini sadece listeye ekle, POS'a gönderme
      handleCreditCardPaymentOnly(remainingAmount)
    } else if (selectedPaymentMethod === 'meal-card') {
      // Open meal card modal to select Multinet or Sodexo
      openMealCardModal()
    }
  }

  // Helper function to create new sale record when first item is added
  async function createNewSaleRecord() {
    try {
      console.log('🆕 Creating new sale record for first item...')

      // Validate user authentication
      const userValidation = validateUserAuthentication()
      if (!userValidation.valid) {
        showError(userValidation.error)
        throw new Error(userValidation.error)
      }

      const user = userValidation.user

      // Prepare sale data for in-progress sale creation
      const saleData = {
        employeeUuid: user.uuid || user.id,
        workstationId: 'ws-001', // Default workstation ID
        customerId: null,
      }

      console.log('📊 Creating in-progress sale with data:', saleData)

      // Create in-progress sale record
      const result = await createInProgressSale(saleData)

      if (result.success) {
        currentSaleUuid = result.saleUuid
        isInProgressSale = true

        console.log('✅ New sale record created:', {
          saleUuid: result.saleUuid,
          receiptNumber: result.receiptNumber,
          saleId: result.saleId,
        })

        showInfo(`Yeni satış başlatıldı (${result.receiptNumber})`)
      } else {
        throw new Error('Sale record creation failed')
      }
    } catch (error) {
      console.error('❌ Error creating new sale record:', error)
      showError(`Satış kaydı oluşturulamadı: ${error.message}`)
      throw error
    }
  }

  // Helper function to validate user authentication
  function validateUserAuthentication() {
    let user = null
    const unsubscribe = currentUser.subscribe(value => {
      user = value
    })
    unsubscribe()

    if (!user) {
      return { valid: false, error: 'Kullanıcı oturumu bulunamadı. Lütfen tekrar giriş yapın.' }
    }

    if (!user.id && !user.uuid) {
      return { valid: false, error: 'Kullanıcı bilgileri eksik. Lütfen tekrar giriş yapın.' }
    }

    return { valid: true, user }
  }

  // SALES MANAGEMENT FUNCTIONS
  async function checkForInProgressSale() {
    console.log('  geldi')
    try {
      const inProgressSale = await getTodaysInProgressSale()
      if (inProgressSale) {
        currentSale = inProgressSale
        currentSaleUuid = inProgressSale.uuid
        isInProgressSale = true

        // Convert database items to salesItems format
        if (inProgressSale.items && inProgressSale.items.length > 0) {
          salesItems = inProgressSale.items.map((item, index) => ({
            sequenceNo: index + 1,
            id: item.id,
            name: item.name,
            unit: item.unit || 'adet',
            price: item.price,
            original_price: item.original_price || item.price, // original_price yoksa price kullan
            inventory_code: item.inventory_code,
            barcode: item.barcode || '',
            quantity: item.quantity,
            total: item.total,
          }))
          sequenceNumber = salesItems.length + 1
        }

        showSuccess('Devam eden satış yüklendi')
      }
    } catch (error) {
      console.error('❌ Error checking for in-progress sale:', error)
    }
  }

  async function addItemToCurrentSale(item) {
    if (currentSaleUuid) {
      try {
        console.log('📦 Ürün devam eden satışa ekleniyor...', item)
        await addItemToInProgressSale(currentSaleUuid, {
          inventory_code: item.inventory_code,
          quantity: getMinimumQuantity(item.unit || 'adet'),
          unit_price: parseFloat(item.price) || 0,
        })
        console.log('✅ Ürün devam eden satışa eklendi')
      } catch (error) {
        console.error('❌ Ürün ekleme hatası:', error)
      }
    }
  }

  async function completeSale() {
    // Enhanced payment validation
    const validation = validatePayments()

    if (!validation.valid) {
      // Show payment method selection modal if no payments made
      if (partialPayments.length === 0) {
        openPaymentMethodModal()
        return
      }

      // Show appropriate error message based on validation type
      if (validation.type === 'error') {
        showError(validation.message)
      } else if (validation.type === 'warning') {
        showError(validation.message)
      } else {
        showInfo(validation.message)
      }
      return
    }

    // Log successful validation
    console.log('✅ Payment validation passed:', validation.message)

    // If we reach here, payments equal transaction amount (within tolerance)

    try {
      // Check if we have credit card payments that were added without authorization (split payments)
      //  const creditCardPayments = partialPayments.filter(p => p.method === 'credit-card')

      // if (creditCardPayments.length > 0) {
      // For split payments with credit cards, send final authorization
      //  showInfo("Kredi kartı ödemeleri POS'a gönderiliyor...")
      try {
        //await sendRealSaleData()
        showSuccess('POS ödemesi başarıyla tamamlandı!')
      } catch (posError) {
        console.error('❌ POS ödeme hatası:', posError)
        // POS hatası durumunda işlemi durdur
        return
      }
      //    showSuccess('POS authorization başarılı!')
      //  }

      // If we have an in-progress sale, complete it
      if (currentSaleUuid && isInProgressSale) {
        await completeInProgressSale(currentSaleUuid, partialPayments)
      } else {
        // Create and save sale transaction
        const salesNumber = await generateSalesNumber()

        // Validate user authentication
        const userValidation = validateUserAuthentication()
        if (!userValidation.valid) {
          showError(userValidation.error)
          return
        }

        const user = userValidation.user
        console.log('👤 Current user validated:', user)

        // Validate user exists in employees table
        let validEmployeeUuid = null
        if (user.uuid) {
          console.log('🔍 Using UUID for employee lookup:', user.uuid)
          validEmployeeUuid = user.uuid
        } else if (user.id) {
          console.log('⚠️ Using ID instead of UUID, need to map to employees table:', user.id)
          // The user.id should correspond to employees.id, and we need the employees.uuid
          // We'll let the main process handle this mapping
          validEmployeeUuid = user.id
        }

        if (!validEmployeeUuid) {
          throw new Error('Valid employee UUID or ID not found')
        }

        // Prepare sale data object for database
        const totalOriginalPrice = salesItems.reduce(
          (sum, item) => sum + (item.original_price || item.price) * item.quantity,
          0
        )

        const saleData = {
          sales_number: salesNumber,
          user_id: user.id || user.uuid,
          employeeUuid: validEmployeeUuid, // Use the validated UUID or ID
          workstationId: 'ws-001', // Varsayılan workstation
          totalPrice: totalAmount,
          originalPrice: totalOriginalPrice, // Main process'te beklenen alan adı
          discount: 0, // İndirim varsa burada hesaplanabilir
          customerId: null, // Müşteri ID'si varsa burada
          total_amount: totalAmount,
          discount_amount: 0, // İndirim varsa burada hesaplanabilir
          tax_amount: 0, // KDV hesabı varsa burada yapılabilir
          status: 4,
          items: salesItems.map(item => ({
            inventory_id: item.id,
            inventory_code: item.inventory_code,
            product_name: item.name,
            unit_price: item.price,
            original_price: item.original_price || item.price,
            quantity: item.quantity,
            unit: item.unit,
            total_price: item.total,
            barcode: item.barcode,
            weight: 0, // Ağırlık varsa burada hesaplanabilir
            weight_unit: null,
            discount: 0, // Ürün bazında indirim varsa burada
          })),
          payments: partialPayments.map(payment => ({
            method: payment.method,
            amount: payment.amount,
            timestamp: payment.timestamp,
          })),
        }
        console.clear()
        console.log(saleData)

        // Validate sale data before saving
        if (!saleData.items || saleData.items.length === 0) {
          throw new Error('Satış ürünleri eksik')
        }

        if (!saleData.payments || saleData.payments.length === 0) {
          throw new Error('Ödeme bilgileri eksik')
        }

        if (saleData.totalPrice <= 0) {
          throw new Error('Geçersiz satış tutarı')
        }

        console.log('💾 Satış verisi veritabanına kaydediliyor:', saleData)
        await saveSaleTransaction(saleData)
      }

      // Reset all sale state
      currentSale = null
      currentSaleUuid = null
      isInProgressSale = false
      salesItems = []
      partialPayments = []
      totalPaidAmount = 0
      sequenceNumber = 1

      showSuccess('Satış başarıyla tamamlandı!')
    } catch (error) {
      console.error('❌ Error completing sale:', error)

      if (error.message.includes('Kullanıcı')) {
        showError(`Kullanıcı hatası: ${error.message}`)
      } else if (error.message.includes('database') || error.message.includes('Database')) {
        showError('Veritabanı hatası. Lütfen tekrar deneyin.')
      } else if (error.message.includes('network') || error.message.includes('Network')) {
        showError('Bağlantı hatası. Lütfen tekrar deneyin.')
      } else {
        showError(`Satış tamamlanamadı: ${error.message}`)
      }
    }
  }

  // INITIALIZE
  onMount(() => {
    // Check for existing in-progress sale
    checkForInProgressSale()

    searchInput?.focus()
    updatePaymentDisplay()

    // CustomerScreen'i aç
    openCustomerScreen()

    // Event listeners
    document.addEventListener('keydown', handleGlobalKeyDown)
    document.addEventListener('click', handleClickOutside)
    document.addEventListener('virtualKeyboardSearch', handleVirtualKeyboardSearch)

    return () => {
      if (searchTimeout) clearTimeout(searchTimeout)
      if (barcodeTimeout) clearTimeout(barcodeTimeout)
      closeDropdown()
      document.removeEventListener('keydown', handleGlobalKeyDown)
      document.removeEventListener('click', handleClickOutside)
      document.removeEventListener('virtualKeyboardSearch', handleVirtualKeyboardSearch)
    }
  })

  function handlePageKeydown(event) {
    // Only respond to Enter or Space keys for accessibility
    if (event.key === 'Enter' || event.key === ' ') {
      handlePageClick(event)
    }
  }
</script>

<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
<main class="sales-page" on:click={handlePageClick} on:keydown={handlePageKeydown} tabindex="-1">
  <div class="main-layout">
    <!-- Left Panel: Sales Screen (25%) -->
    <div class="sales-panel">
      <!-- Tab Section -->
      <div class="tab-section">
        <div class="tabs is-boxed">
          <ul>
            <li class={activeTab === 'search' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('search')}
                class="tab-button"
                aria-label="Ürün Ara"
              >
                <span class="icon is-small"><i class="fas fa-search"></i></span>
                <span>Ürün Ara</span>
              </button>
            </li>
            <li class={activeTab === 'food' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('food')}
                class="tab-button"
                aria-label="Gıda"
              >
                <span>Gıda</span>
              </button>
            </li>
            <li class={activeTab === 'grocery' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('grocery')}
                class="tab-button"
                aria-label="Manav"
              >
                <span>Manav</span>
              </button>
            </li>
            <li class={activeTab === 'cleaning' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('cleaning')}
                class="tab-button"
                aria-label="Temizlik"
              >
                <span>Temizlik</span>
              </button>
            </li>
            <li class={activeTab === 'clothing' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('clothing')}
                class="tab-button"
                aria-label="Giyim"
              >
                <span>Giyim</span>
              </button>
            </li>
          </ul>
        </div>

        <div class="tab-content">
          {#if activeTab === 'search'}
            <!-- Search Tab Content -->
            <div class="search-tab-content">
              <div class="field has-addons">
                <div class="control is-expanded has-icons-left autocomplete-container">
                  <input
                    bind:this={searchInput}
                    bind:value={searchTerm}
                    on:input={handleInputChange}
                    on:keydown={handleKeyDown}
                    on:blur={handleInputBlur}
                    class="input is-large search-input"
                    type="text"
                    placeholder="Ürün adı, barkod veya ürün kodu ile arama yapın..."
                    disabled={isSearching}
                    autocomplete="off"
                  />
                  <span class="icon is-left">
                    <i class="fas fa-search"></i>
                  </span>
                </div>
                <div class="control">
                  <button
                    on:click={handleAddButtonClick}
                    class="button is-primary is-large {isSearching ? 'is-loading' : ''}"
                  >
                    <span class="icon">
                      <i class="fas fa-plus"></i>
                    </span>
                    <span>Ekle</span>
                  </button>
                </div>
              </div>

              <!-- DSL Ürünleri Checkbox -->
              <div class="field" style="margin-top: 1rem;">
                <div class="control">
                  <label class="checkbox">
                    <input type="checkbox" bind:checked={showDLSProducts} />
                    <span style="margin-left: 0.5rem; font-weight: 500; color: #374151;">
                      DLS ürünleri listele
                    </span>
                  </label>
                </div>
              </div>
            </div>
          {:else}
            <!-- Category Tabs Content (placeholder grid) -->
            <div class="category-tab-content">
              <div class="category-grid">
                <div class="category-item">
                  <div class="category-image-placeholder">
                    <i class="fas fa-image"></i>
                  </div>
                  <span class="category-name">Ürün 1</span>
                </div>
                <div class="category-item">
                  <div class="category-image-placeholder">
                    <i class="fas fa-image"></i>
                  </div>
                  <span class="category-name">Ürün 2</span>
                </div>
                <div class="category-item">
                  <div class="category-image-placeholder">
                    <i class="fas fa-image"></i>
                  </div>
                  <span class="category-name">Ürün 3</span>
                </div>
                <div class="category-item">
                  <div class="category-image-placeholder">
                    <i class="fas fa-image"></i>
                  </div>
                  <span class="category-name">Ürün 4</span>
                </div>
              </div>
            </div>
          {/if}
        </div>
      </div>

      <!-- Sales Items Table -->
      <div class="sales-table-section">
        {#if salesItems.length === 0}
          <div class="notification is-light">
            <div class="has-text-centered">
              <span class="icon is-large has-text-grey-light">
                <i class="fas fa-shopping-basket fa-3x"></i>
              </span>
              <p class="title is-6 has-text-grey">Henüz ürün eklenmedi</p>
              <p class="subtitle is-7 has-text-grey">
                Yukarıdaki arama kutusunu kullanarak ürün ekleyebilirsiniz
              </p>
            </div>
          </div>
        {:else}
          <div class="table-container">
            <table class="table is-fullwidth is-striped is-hoverable sales-table">
              <thead>
                <tr>
                  <th class="col-sequence"></th>
                  <th class="col-product">Ürün</th>
                  <th class="col-unit">Birim</th>
                  <th class="col-price has-text-right">Fiyat</th>
                  <th class="col-quantity">Miktar</th>
                  <th class="col-total has-text-right">Toplam</th>
                  <th class="col-actions"></th>
                </tr>
              </thead>
              <tbody>
                {#each salesItems as item, index (item.sequenceNo)}
                  <tr>
                    <td>
                      <span class="tag is-rounded">{item.sequenceNo}</span>
                    </td>
                    <td>
                      <strong>{item.name}</strong>
                      <br />
                      <small class="has-text-grey">
                        {#if item.barcode}
                          | Barkod: {item.barcode}{/if}
                      </small>
                    </td>
                    <td>{item.unit}</td>
                    <td class="has-text-right">
                      <span class="decimal-aligned">
                        {item.price.toLocaleString('tr-TR', {
                          style: 'currency',
                          currency: 'TRY',
                        })}
                      </span>
                    </td>
                    <td>
                      <div class="field has-addons quantity-controls">
                        <!-- Decrease Button -->
                        <div class="control">
                          <button
                            on:click={() => decreaseQuantity(index)}
                            class="button is-small quantity-btn decrement"
                            title="Miktarı azalt"
                          >
                            <span class="icon">
                              <i class="fas fa-minus"></i>
                            </span>
                          </button>
                        </div>

                        <!-- Quantity Input -->
                        <div class="control">
                          <input
                            value={formatQuantity(item.quantity)}
                            on:input={event => handleQuantityChange(index, event.target.value)}
                            on:keydown={event => handleQuantityKeydown(event, item.unit)}
                            class="input is-small has-text-centered"
                            type="text"
                            placeholder={isDecimalUnit(item.unit) ? '1,50' : '1'}
                            title={isDecimalUnit(item.unit)
                              ? `${item.unit} birimi için ondalık değer giriniz (örn: 1,50 veya 2,25)`
                              : `${item.unit} birimi için tam sayı giriniz (örn: 1, 2, 3)`}
                            style="width: 80px;"
                          />
                        </div>

                        <!-- Increase Button -->
                        <div class="control">
                          <button
                            on:click={() => increaseQuantity(index)}
                            class="button is-small quantity-btn increment"
                            title="Miktarı artır"
                          >
                            <span class="icon">
                              <i class="fas fa-plus"></i>
                            </span>
                          </button>
                        </div>
                      </div>
                    </td>
                    <td class="has-text-right">
                      <strong class="decimal-aligned">
                        {item.total.toLocaleString('tr-TR', {
                          style: 'currency',
                          currency: 'TRY',
                        })}
                      </strong>
                    </td>
                    <td>
                      <button
                        on:click={() => removeItem(index)}
                        class="button is-small is-danger is-outlined"
                        title="Ürünü kaldır"
                      >
                        <span class="icon">
                          <i class="fas fa-trash"></i>
                        </span>
                      </button>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        {/if}
      </div>
    </div>

    <!-- Right Panel: POS Payment System (75%) -->
    <div class="content-panel">
      <!-- Payment Summary Header -->
      <div class="payment-header">
        <div class="payment-summary">
          <div class="summary-item">
            <span class="summary-label">İndirim Tutarı</span>
            <span class="summary-value discount">₺ 0</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Ödenen Tutar</span>
            <span class="summary-value paid"
              >{totalPaidAmount.toLocaleString('tr-TR', {
                style: 'currency',
                currency: 'TRY',
              })}</span
            >
          </div>
        </div>
        <div class="total-remaining-container">
          <div class="total-amount-container tags has-addons">
            <span class="tag total-label">Toplam Tutar</span>
            <span class="tag total-amount decimal-aligned">
              {totalAmount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' })}
            </span>
          </div>
          <div class="remaining-amount-container tags has-addons">
            <span class="tag remaining-label">Kalanı Öde</span>
            <button
              class="tag remaining-amount-btn"
              class:active={remainingAmount > 0}
              disabled={remainingAmount <= 0}
              on:click={payRemainingAmount}
            >
              {remainingAmount.toLocaleString('tr-TR', {
                style: 'currency',
                currency: 'TRY',
              })}
            </button>
          </div>
        </div>
      </div>

      <!-- Payment Methods and Keypad -->
      <div class="payment-content">
        <!-- Left Side - Payment Methods -->
        <div class="payment-methods">
          <button class="payment-btn meal-card" on:click={() => selectPaymentMethod('meal-card')}
            >Yemek Kartı</button
          >
          <button
            class="payment-btn cash-drawer"
            on:click={() => selectPaymentMethod('cash-drawer')}>P.Çekmece Aç</button
          >

          <button class="payment-btn cash" on:click={() => selectPaymentMethod('cash')}
            >Nakit</button
          >
          <button
            class="payment-btn credit-card"
            on:click={() => selectPaymentMethod('credit-card')}>Kredi Kartı</button
          >

          {#if isPaymentComplete}
            <button class="complete-sale-btn2 is-success" on:click={completeSale}>
              SATIŞI TAMAMLA ✓
            </button>
          {:else}
            <button class="complete-sale-btn2" disabled>
              SATIŞI TAMAMLA <br />
              (Kalan: {formatTurkishCurrency(remainingAmount)})
            </button>
          {/if}
        </div>

        <!-- Center - Numeric Keypad -->
        <div class="numeric-keypad">
          <div class="amount-display">
            <input
              type="text"
              class="amount-input"
              bind:value={paymentAmount}
              placeholder="0"
              readonly
            />
          </div>
          <table class="keypad-table">
            <tbody>
              <tr class="keypad-row">
                <td><button class="key-btn" on:click={() => addToAmount('7')}>7</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('8')}>8</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('9')}>9</button></td>
                <td><button class="key-btn backspace" on:click={removeLastDigit}>⌫</button></td>
              </tr>
              <tr class="keypad-row">
                <td><button class="key-btn" on:click={() => addToAmount('4')}>4</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('5')}>5</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('6')}>6</button></td>
                <td><button class="key-btn clear" on:click={clearAmount}>C</button></td>
              </tr>
              <tr class="keypad-row">
                <td><button class="key-btn" on:click={() => addToAmount('1')}>1</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('2')}>2</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('3')}>3</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('00')}>00</button></td>
              </tr>
              <tr class="keypad-row">
                <td colspan="2">
                  <button class="key-btn zero" on:click={() => addToAmount('0')}>0</button>
                </td>
                <td><button class="key-btn decimal" on:click={() => addToAmount('.')}>.</button></td
                >
                <td></td>
              </tr>
            </tbody>
          </table>

          <!-- Enhanced Split Payments List -->
          {#if partialPayments.length > 0}
            <div class="split-payments-container">
              <div class="split-payments-header">
                <h4 class="split-payments-title">
                  <span class="icon">
                    <i class="fas fa-credit-card"></i>
                  </span>
                  Bölünmüş Ödemeler ({partialPayments.length})
                </h4>
                <div class="payment-progress">
                  <div class="progress-bar">
                    <div
                      class="progress-fill"
                      style="width: {totalAmount > 0 ? (totalPaidAmount / totalAmount) * 100 : 0}%"
                    ></div>
                  </div>
                  <span class="progress-text">
                    {totalPaidAmount.toFixed(2)} / {totalAmount.toFixed(2)} TL
                  </span>
                </div>
              </div>

              <div class="split-payments-list">
                {#each partialPayments as payment (payment.id)}
                  <div class="split-payment-item">
                    <div class="payment-info">
                      <div
                        class="payment-method-badge"
                        class:cash={payment.method === 'cash'}
                        class:credit-card={payment.method === 'credit-card'}
                        class:meal-card={payment.method === 'meal-card'}
                      >
                        <span class="method-icon">
                          {#if payment.method === 'cash'}
                            💵
                          {:else if payment.method === 'credit-card'}
                            💳
                          {:else if payment.method === 'meal-card'}
                            🍽️
                          {/if}
                        </span>
                        <span class="method-name">{payment.methodDisplayName}</span>
                      </div>

                      <div class="payment-amount-display">
                        <span class="amount">{payment.amount.toFixed(2)} TL</span>
                        <div class="payment-actions">
                          <button
                            class="btn-remove"
                            on:click={() => removePartialPayment(payment.id)}
                          >
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                {/each}
              </div>
            </div>
          {/if}
        </div>

        <!-- Right Side - Quick Actions -->
        <div class="quick-actions">
          <button class="action-btn loyalty">Birlik Kart</button>
          <button class="action-btn premium">Premium Kart</button>
          <button class="action-btn gift">Hediye Çeki</button>
          <button class="action-btn receipt">Fiş Beklemeye Al</button>
          <button class="action-btn is-warning">Satış İptal</button>
          <button class="action-btn is-info">Cari Ekle</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Payment Processing Modal (Unclosable) -->
  {#if showPaymentProcessingModal}
    <div class="modal is-active">
      <!-- No modal-background click event to prevent closing -->
      <div class="modal-background"></div>
      <div class="modal-card">
        <header class="modal-card-head has-background-primary">
          <p class="modal-card-title has-text-white">
            <span class="icon has-text-white">
              <i class="fas fa-credit-card"></i>
            </span>
            Ödeme İşlemi
          </p>
          <!-- No close button -->
        </header>
        <section class="modal-card-body has-text-centered">
          <div class="payment-processing-content">
            <div class="loading-animation">
              <span class="icon is-large has-text-primary">
                <i class="fas fa-money-bill-wave fa-3x"></i>
              </span>
            </div>
            <h3 class="title is-4 has-text-primary">Ödeme Alınıyor</h3>
            <p class="subtitle is-6 has-text-grey">Lütfen bekleyiniz, işlem devam ediyor...</p>
            <div class="progress-indicator">
              <progress class="progress is-primary" max="100">60%</progress>
            </div>
          </div>
        </section>
        <!-- No footer with close button -->
      </div>
    </div>
  {/if}

  <!-- Enhanced Cash Change Calculation Modal -->
  {#if showCashChangeModal}
    <div class="modal is-active">
      <div class="modal-background"></div>
      <div class="modal-card">
        <header class="modal-card-head has-background-success">
          <p class="modal-card-title has-text-white">
            <span class="icon has-text-white">
              <i class="fas fa-money-bill-wave"></i>
            </span>
            Para Üstü Hesaplama
          </p>
        </header>
        <section class="modal-card-body">
          <div class="cash-change-calculation">
            <!-- Sale Amount -->
            <div class="field">
              <label class="label">Toplam Satış Tutarı</label>
              <div class="control">
                <div class="notification is-info is-light">
                  <span class="tag is-large is-info">
                    {remainingAmount.toLocaleString('tr-TR', {
                      style: 'currency',
                      currency: 'TRY',
                    })}
                  </span>
                </div>
              </div>
            </div>

            <div class="field">
              <label class="label" for="cash-received">Alınan Nakit</label>
              <div class="control">
                <div class="tags has-addons">
                  <span class="tag is-primary is-large">
                    {cashReceived.toLocaleString('tr-TR', {
                      style: 'currency',
                      currency: 'TRY',
                    })}
                  </span>
                </div>
              </div>
            </div>

            <!-- Change Amount Display -->
            <div class="field">
              <label class="label">Para Üstü</label>
              <div class="control">
                <div class="notification is-success is-light">
                  <span class="tag is-large is-success">
                    {changeAmount.toLocaleString('tr-TR', {
                      style: 'currency',
                      currency: 'TRY',
                    })}
                  </span>
                </div>
              </div>
            </div>

            <!-- Calculation Summary -->
            <div class="notification is-primary is-light">
              <div class="content">
                <h4 class="title is-5">💰 Hesaplama Özeti</h4>
                <div class="columns">
                  <div class="column">
                    <p><strong>Ödenen Tutar:</strong></p>
                    <p class="title is-6">{cashReceived.toFixed(2)} TL</p>
                  </div>
                  <div class="column">
                    <p><strong>Satış Tutarı:</strong></p>
                    <p class="title is-6">{remainingAmount.toFixed(2)} TL</p>
                  </div>
                  <div class="column">
                    <p><strong>Para Üstü:</strong></p>
                    <p class="title is-6 has-text-success">{changeAmount.toFixed(2)} TL</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-success is-large"
            on:click={completeCashSaleWithChange}
            style="font-size: 1.2rem; font-weight: 700; text-transform: uppercase; letter-spacing: 0.5px; min-height: 60px;"
          >
            <span class="icon">
              <i class="fas fa-check-circle"></i>
            </span>
            <span>Satışı Tamamla</span>
          </button>
        </footer>
      </div>
    </div>
  {/if}

  <!-- Payment Method Selection Modal -->
  {#if showPaymentMethodModal}
    <div class="modal is-active">
      <div
        class="modal-background"
        on:click={closePaymentMethodModal}
        on:keydown={closePaymentMethodModal}
        role="button"
        tabindex="0"
      ></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">
            <span class="icon">
              <i class="fas fa-credit-card"></i>
            </span>
            Ödeme Yöntemi Seçin
          </p>
          <button class="delete" aria-label="close" on:click={closePaymentMethodModal}></button>
        </header>
        <section class="modal-card-body">
          <div class="payment-method-selection">
            <div class="field">
              <label class="label">Toplam Tutar</label>
              <div class="control">
                <div class="tags has-addons">
                  <span class="tag is-large is-primary">
                    {totalAmount.toLocaleString('tr-TR', {
                      style: 'currency',
                      currency: 'TRY',
                    })}
                  </span>
                </div>
              </div>
            </div>

            <div class="field">
              <label class="label">Ödeme yöntemini seçin:</label>
              <div class="control">
                <div class="payment-method-buttons">
                  <button
                    class="button is-large is-success payment-method-option"
                    on:click={() => selectPaymentMethodFromModal('cash')}
                  >
                    <span class="icon">
                      <i class="fas fa-money-bill-wave"></i>
                    </span>
                    <span>Nakit</span>
                  </button>

                  <button
                    class="button is-large is-info payment-method-option"
                    on:click={() => selectPaymentMethodFromModal('credit-card')}
                  >
                    <span class="icon">
                      <i class="fas fa-credit-card"></i>
                    </span>
                    <span>Kredi Kartı</span>
                  </button>

                  <button
                    class="button is-large is-warning payment-method-option"
                    on:click={() => selectPaymentMethodFromModal('split')}
                  >
                    <span class="icon">
                      <i class="fas fa-divide"></i>
                    </span>
                    <span>Bölünmüş Ödeme</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button class="button" on:click={closePaymentMethodModal}>İptal</button>
        </footer>
      </div>
    </div>
  {/if}

  <!-- Meal Card Selection Modal -->
  {#if showMealCardModal}
    <div class="modal is-active">
      <div
        class="modal-background"
        on:click={closeMealCardModal}
        on:keydown={closeMealCardModal}
        role="button"
        tabindex="0"
      ></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">
            <span class="icon">
              <i class="fas fa-utensils"></i>
            </span>
            Yemek Kartı Seçin
          </p>
          <button class="delete" aria-label="close" on:click={closeMealCardModal}></button>
        </header>
        <section class="modal-card-body">
          <div class="meal-card-selection">
            <div class="field">
              <label class="label">Yemek kartı türünü seçin:</label>
              <div class="control">
                <div class="meal-card-buttons">
                  <button
                    class="button meal-card-option"
                    on:click={() => selectMealCardType('Multinet')}
                  >
                    <img
                      src="/icons/multinet.png"
                      style="height: 60px; width: auto; margin-right: 10px;"
                      alt="Multinet Logo"
                    />
                  </button>

                  <button
                    class="button meal-card-option"
                    on:click={() => selectMealCardType('METROPOLCARD')}
                  >
                    <img
                      src="/icons/metropolcard.png"
                      style="height: 60px; width: auto; margin-right: 10px;"
                      alt="METROPOLCARD Logo"
                    />
                  </button>

                  <button
                    class="button meal-card-option"
                    on:click={() => selectMealCardType('Edenred')}
                  >
                    <img
                      src="/icons/edenred.png"
                      style="height: 60px; width: auto; margin-right: 10px;"
                      alt="Edenred Logo"
                    />
                  </button>

                  <button
                    class="button meal-card-option"
                    on:click={() => selectMealCardType('SETCARD')}
                  >
                    <img
                      src="/icons/setcard.png"
                      style="height: 60px; width: auto; margin-right: 10px;"
                      alt="SetCard Logo"
                    />
                  </button>

                  <button
                    class="button meal-card-option"
                    on:click={() => selectMealCardType('iWallet')}
                  >
                    <img
                      src="/icons/iwallet.png"
                      style="height: 60px; width: auto; margin-right: 10px;"
                      alt="iWallet Logo"
                    />
                  </button>

                  <button
                    class="button meal-card-option"
                    on:click={() => selectMealCardType('PLUXEE')}
                  >
                    <img
                      src="/icons/pluxee.png"
                      style="height: 60px; width: auto; margin-right: 10px;"
                      alt="PLUXEE Logo"
                    />
                  </button>

                  <button
                    class="button meal-card-option"
                    on:click={() => selectMealCardType('TOKENFLEX')}
                  >
                    <img
                      src="/icons/tokenflex.png"
                      style="height: 60px; width: auto; margin-right: 10px;"
                      alt="TOKENFLEX Logo"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button class="button" on:click={closeMealCardModal}>İptal</button>
        </footer>
      </div>
    </div>
  {/if}

  <!-- Quantity Modal -->
  {#if showQuantityModal}
    <div class="modal is-active">
      <div
        class="modal-background"
        on:click={closeQuantityModal}
        on:keydown={closeQuantityModal}
        role="button"
        tabindex="0"
      ></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">
            <span class="icon">
              <i class="fas fa-weight"></i>
            </span>
            Miktar Girişi
          </p>
          <button class="delete" aria-label="close" on:click={closeQuantityModal}></button>
        </header>
        <section class="modal-card-body">
          {#if quantityModalItem}
            <div class="quantity-input-form">
              <div class="field">
                <label class="label" for="product-name">Ürün</label>
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag is-primary is-large">
                      {quantityModalItem.name}
                    </span>
                  </div>
                </div>
              </div>

              <div class="field">
                <label class="label" for="product-price">Birim Fiyat</label>
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag is-info is-large">
                      {quantityModalItem.price.toLocaleString('tr-TR', {
                        style: 'currency',
                        currency: 'TRY',
                      })} / {quantityModalItem.unit}
                    </span>
                  </div>
                </div>
              </div>

              <div class="field">
                <label class="label" for="quantity-input">Miktar ({quantityModalItem.unit})</label>
                <div class="control">
                  <input
                    id="quantity-input"
                    class="input is-large has-text-centered"
                    type="text"
                    placeholder="0,00"
                    bind:value={quantityInput}
                    on:keydown={e => {
                      if (e.key === 'Enter') {
                        confirmQuantity()
                      }
                      handleQuantityKeydown(e, quantityModalItem.unit)
                    }}
                    autocomplete="off"
                  />
                </div>
                <p class="help">Ondalık ayracı için virgül (,) kullanın</p>
              </div>
            </div>
          {/if}
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-success is-large"
            on:click={confirmQuantity}
            disabled={!quantityInput || quantityInput.trim() === ''}
          >
            <span class="icon">
              <i class="fas fa-check"></i>
            </span>
            <span>Tamam</span>
          </button>
          <button class="button" on:click={closeQuantityModal}>İptal</button>
        </footer>
      </div>
    </div>
  {/if}
</main>

<style>
  @import './Home.css';

  .payment-method-selection {
    text-align: center;
  }

  .payment-method-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
  }

  .payment-method-option {
    justify-content: flex-start;
    padding: 1.5rem;
  }

  .meal-card-selection {
    text-align: center;
  }

  .meal-card-buttons {
    display: flex;
    margin-top: 1rem;
    flex-wrap: wrap;
    gap: 3rem;
  }

  .meal-card-option {
    justify-content: flex-start;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    background-color: transparent !important;
    border: 2px solid #dbdbdb;
    transition: all 0.3s ease;
  }

  .meal-card-option:hover {
    border-color: #3273dc;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .meal-card-option img {
    object-fit: contain;
  }

  .meal-card-option span {
    font-weight: 600;
    font-size: 1rem;
  }

  /* Payment Processing Modal Styles */
  .payment-processing-content {
    padding: 2rem;
  }

  .loading-animation {
    margin-bottom: 1.5rem;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.7;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .progress-indicator {
    margin-top: 1.5rem;
  }

  /* Ensure modal cannot be closed */
  .modal.is-active .modal-background {
    pointer-events: none;
  }
</style>
