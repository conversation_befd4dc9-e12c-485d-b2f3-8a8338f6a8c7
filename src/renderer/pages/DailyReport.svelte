<script>
  import { onMount } from 'svelte'
  import { authStore } from '../stores/authStore.js'
  import { showError, showSuccess } from '../utils/toastUtils.js'

  console.log('📊 DailyReport.svelte loaded')

  // Authentication state
  $: authState = $authStore

  // Report state
  let isLoading = false
  let reportDate = new Date().toISOString().split('T')[0]
  let reportData = {
    paymentSummary: [],
    dailyTotal: 0,
    cancelTotal: 0,
    refundTotal: 0,
    userSales: [],
    allSales: [],
  }

  onMount(async () => {
    console.log('📊 DailyReport component mounted')
    console.log('📊 reportDate:', reportDate)
    console.log('📊 window.electronAPI:', window.electronAPI)
    console.log('📊 getDailyReport function:', window.electronAPI?.getDailyReport)
    await loadDailyReport()
  })

  async function loadDailyReport() {
    console.log('📊 loadDailyReport called with date:', reportDate)

    if (!window.electronAPI?.getDailyReport) {
      console.error('❌ getDailyReport function not available')
      showError('Günlük rapor servisi mevcut değil')
      return
    }

    isLoading = true
    try {
      console.log('📊 Calling getDailyReport...')
      const result = await window.electronAPI.getDailyReport(reportDate)
      console.log('📊 getDailyReport result:', result)

      if (result.success) {
        reportData = result.data
        console.log('📊 Report data loaded:', reportData)
        showSuccess('Günlük rapor başarıyla yüklendi')
      } else {
        console.error('❌ getDailyReport failed:', result.error)
        showError(result.error || 'Günlük rapor yüklenirken hata oluştu')
      }
    } catch (error) {
      console.error('❌ Daily report error:', error)
      showError('Günlük rapor yüklenirken hata oluştu')
    } finally {
      isLoading = false
    }
  }

  async function createTestData() {
    try {
      console.log('📊 Creating test data...')
      const result = await window.electronAPI.createTestSalesData()
      if (result.success) {
        console.log('✅ Test data created successfully')
        showSuccess('Test verileri başarıyla oluşturuldu')
        await loadDailyReport()
      } else {
        console.error('❌ Failed to create test data:', result.error)
        showError(`Test verileri oluşturulurken hata oluştu: ${result.error}`)
      }
    } catch (error) {
      console.error('❌ Error creating test data:', error)
      showError('Test verileri oluşturulurken hata oluştu')
    }
  }

  function formatCurrency(amount) {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(amount || 0)
  }

  function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString('tr-TR')
  }

  function getStatusText(status) {
    switch (status) {
      case 'completed':
        return 'Tamamlandı'
      case 'cancelled':
        return 'İptal'
      case 'refunded':
        return 'İade'
      default:
        return status
    }
  }

  function getStatusClass(status) {
    switch (status) {
      case 'completed':
        return 'has-text-success'
      case 'cancelled':
        return 'has-text-danger'
      case 'refunded':
        return 'has-text-warning'
      default:
        return ''
    }
  }
</script>

<div class="daily-report">
  <div class="container">
    <div class="columns">
      <div class="column">
        <h1 class="title">📊 Günlük Satış Raporu</h1>

        <!-- Tarih Seçici ve Kontroller -->
        <div class="box">
          <div class="field is-grouped">
            <div class="control">
              <label class="label" for="report-date">Rapor Tarihi</label>
              <input
                id="report-date"
                class="input"
                type="date"
                bind:value={reportDate}
                on:change={loadDailyReport}
              />
            </div>
            <div class="control">
              <label class="label">&nbsp;</label>
              <button class="button is-primary" on:click={loadDailyReport}>
                <span class="icon">
                  <i class="fas fa-sync-alt"></i>
                </span>
                <span>Raporu Yenile</span>
              </button>
            </div>
            <div class="control">
              <label class="label">&nbsp;</label>
              <button class="button is-warning" on:click={createTestData}>
                <span class="icon">
                  <i class="fas fa-database"></i>
                </span>
                <span>Test Verileri Oluştur</span>
              </button>
            </div>
          </div>
        </div>

        {#if isLoading}
          <div class="box has-text-centered">
            <div class="is-loading"></div>
            <p class="mt-3">Rapor yükleniyor...</p>
          </div>
        {:else}
          <!-- Genel Toplam Kartları -->
          <div class="columns is-multiline">
            <div class="column is-3">
              <div class="box has-background-primary-light">
                <h3 class="title is-5 has-text-primary">💰 Günlük Toplam</h3>
                <p class="title is-3 has-text-primary">{formatCurrency(reportData.dailyTotal)}</p>
              </div>
            </div>
            <div class="column is-3">
              <div class="box has-background-danger-light">
                <h3 class="title is-5 has-text-danger">❌ İptal Toplam</h3>
                <p class="title is-3 has-text-danger">{formatCurrency(reportData.cancelTotal)}</p>
              </div>
            </div>
            <div class="column is-3">
              <div class="box has-background-warning-light">
                <h3 class="title is-5 has-text-warning">↩️ İade Toplam</h3>
                <p class="title is-3 has-text-warning">{formatCurrency(reportData.refundTotal)}</p>
              </div>
            </div>
            <div class="column is-3">
              <div class="box has-background-success-light">
                <h3 class="title is-5 has-text-success">✅ Net Toplam</h3>
                <p class="title is-3 has-text-success">
                  {formatCurrency(
                    reportData.dailyTotal - reportData.cancelTotal - reportData.refundTotal
                  )}
                </p>
              </div>
            </div>
          </div>

          <!-- Ödeme Türlerine Göre Toplam -->
          <div class="box">
            <h3 class="title is-4">💳 Ödeme Türlerine Göre Toplam</h3>
            {#if reportData.paymentSummary && reportData.paymentSummary.length > 0}
              <div class="table-container">
                <table class="table is-fullwidth is-striped">
                  <thead>
                    <tr>
                      <th>Ödeme Türü</th>
                      <th>Satış Sayısı</th>
                      <th>Toplam Tutar</th>
                    </tr>
                  </thead>
                  <tbody>
                    {#each reportData.paymentSummary as payment (payment.payment_type)}
                      <tr>
                        <td>
                          <span class="tag is-info">{payment.payment_type}</span>
                        </td>
                        <td>
                          <span class="tag is-light">{payment.count} adet</span>
                        </td>
                        <td>
                          <strong>{formatCurrency(payment.total)}</strong>
                        </td>
                      </tr>
                    {/each}
                  </tbody>
                </table>
              </div>
            {:else}
              <div class="notification is-info is-light">
                <p>Bu tarih için ödeme türü verisi bulunamadı.</p>
              </div>
            {/if}
          </div>

          <!-- Personel Bazlı Satış Toplamları -->
          <div class="box">
            <h3 class="title is-4">👥 Personel Bazlı Satış Toplamları</h3>
            {#if reportData.userSales && reportData.userSales.length > 0}
              <div class="table-container">
                <table class="table is-fullwidth is-striped">
                  <thead>
                    <tr>
                      <th>Personel</th>
                      <th>Satış Sayısı</th>
                      <th>Toplam Tutar</th>
                    </tr>
                  </thead>
                  <tbody>
                    {#each reportData.userSales as user (user.username)}
                      <tr>
                        <td>
                          <span class="tag is-primary">{user.username || 'Bilinmiyor'}</span>
                        </td>
                        <td>
                          <span class="tag is-light">{user.count} satış</span>
                        </td>
                        <td>
                          <strong>{formatCurrency(user.total)}</strong>
                        </td>
                      </tr>
                    {/each}
                  </tbody>
                </table>
              </div>
            {:else}
              <div class="notification is-info is-light">
                <p>Bu tarih için personel satış verisi bulunamadı.</p>
              </div>
            {/if}
          </div>

          <!-- Günlük Satış/İade Listesi -->
          <div class="box">
            <h3 class="title is-4">📋 Günlük Satış/İade Listesi</h3>
            {#if reportData.allSales && reportData.allSales.length > 0}
              <div class="table-container">
                <table class="table is-fullwidth is-striped is-hoverable">
                  <thead>
                    <tr>
                      <th>Satış ID</th>
                      <th>Tarih/Saat</th>
                      <th>Personel</th>
                      <th>Toplam Tutar</th>
                      <th>Ödeme Türü</th>
                      <th>Durum</th>
                    </tr>
                  </thead>
                  <tbody>
                    {#each reportData.allSales as sale (sale.id)}
                      <tr>
                        <td>
                          <span class="tag is-dark">#{sale.id}</span>
                        </td>
                        <td>
                          <small>{formatDateTime(sale.created_at)}</small>
                        </td>
                        <td>
                          <span class="tag is-info is-light">
                            {sale.username || 'Bilinmiyor'}
                          </span>
                        </td>
                        <td>
                          <strong>{formatCurrency(sale.total_amount)}</strong>
                        </td>
                        <td>
                          <span class="tag is-primary is-light">
                            {sale.payment_type || 'Belirtilmemiş'}
                          </span>
                        </td>
                        <td>
                          <span class="tag {getStatusClass(sale.status)}">
                            {getStatusText(sale.status)}
                          </span>
                        </td>
                      </tr>
                    {/each}
                  </tbody>
                </table>
              </div>

              <!-- Sayfa Özeti -->
              <div class="notification is-info is-light mt-4">
                <p>
                  <strong>Toplam {reportData.allSales.length} işlem</strong> listelendi. Seçili
                  tarih: <strong>{reportDate}</strong>
                </p>
              </div>
            {:else}
              <div class="notification is-warning is-light">
                <p>
                  <strong>Bu tarih için satış verisi bulunamadı.</strong><br />
                  Test verileri oluşturmak için yukarıdaki "Test Verileri Oluştur" butonunu kullanabilirsiniz.
                </p>
              </div>
            {/if}
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>

<style>
  .daily-report {
    padding: 1rem;
  }

  .box {
    margin-bottom: 1.5rem;
  }

  .table-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
  }

  .is-loading {
    border: 4px solid #f3f3f3;
    border-radius: 50%;
    border-top: 4px solid #3498db;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
    margin: 0 auto;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* Status tag colors */
  .tag.has-text-success {
    background-color: #d1fae5;
    color: #065f46;
  }

  .tag.has-text-danger {
    background-color: #fee2e2;
    color: #991b1b;
  }

  .tag.has-text-warning {
    background-color: #fef3c7;
    color: #92400e;
  }

  /* Responsive table */
  @media screen and (max-width: 768px) {
    .table-container {
      max-height: 300px;
    }

    .table th,
    .table td {
      padding: 0.5rem;
      font-size: 0.875rem;
    }
  }

  /* Enhanced box styling */
  .box {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
  }

  /* Field grouping */
  .field.is-grouped .control {
    margin-right: 1rem;
  }

  .field.is-grouped .control:last-child {
    margin-right: 0;
  }
</style>
