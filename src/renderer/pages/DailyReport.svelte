<script>
  import { onMount } from 'svelte'

  console.log('🔴 DailyReport.svelte script loaded')

  // Simple test state
  let isLoading = false
  let reportDate = new Date().toISOString().split('T')[0]
  let testMessage = 'DailyReport component loaded successfully!'

  onMount(() => {
    console.log('🔴 DailyReport component mounted')
    console.log('🔴 Current URL:', window.location.href)
    console.log('🔴 Current hash:', window.location.hash)
    testMessage = `Component mounted at ${new Date().toLocaleString()}`
  })

  function testFunction() {
    testMessage = `But<PERSON> clicked at ${new Date().toLocaleString()}`
    console.log('🔴 Test button clicked')
  }
</script>

<div class="daily-report">
  <!-- ULTRA SIMPLE TEST -->
  <div
    style="background: red; color: white; padding: 20px; margin: 20px; font-size: 24px; font-weight: bold;"
  >
    <h1>🔴 GÜNLÜK RAPOR SAYFASI TEST</h1>
    <p>Zaman: {new Date().toLocaleString()}</p>
    <p>URL: {window.location.hash}</p>
    <p>Route: /daily-report</p>
    <p>Test Mesajı: {testMessage}</p>
  </div>

  <div class="container">
    <div class="box">
      <h1 class="title">Günlük Satış Raporu - Test Sayfası</h1>

      <div class="notification is-info">
        <p><strong>Sayfa Durumu:</strong> Aktif</p>
        <p><strong>Tarih:</strong> {reportDate}</p>
        <p><strong>Loading:</strong> {isLoading}</p>
        <p><strong>Test Mesajı:</strong> {testMessage}</p>
      </div>

      <div class="field">
        <div class="control">
          <button class="button is-primary is-large" on:click={testFunction}>
            Test Butonu - Tıkla
          </button>
        </div>
      </div>

      <div class="field">
        <label class="label" for="date-input">Tarih Seçici</label>
        <div class="control">
          <input id="date-input" class="input" type="date" bind:value={reportDate} />
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .daily-report {
    padding: 1rem;
  }

  .box {
    margin-bottom: 1.5rem;
  }
</style>
