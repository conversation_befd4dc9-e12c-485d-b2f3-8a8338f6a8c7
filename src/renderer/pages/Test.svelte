<script>
  import { onMount } from 'svelte'
  import { push } from 'svelte-spa-router'
  import { authStore } from '../stores/authStore.js'
  import { getTodaysSales } from '../utils/database.js'
  import { showError, showSuccess } from '../utils/toastUtils.js'

  // Authentication state
  $: authState = $authStore

  // Check authentication and redirect if not authenticated
  $: if (authState && !authState.isLoading && !authState.isAuthenticated) {
    push('/login')
  }

  // Sales state
  let todaysSales = []
  let isLoadingSales = false
  let lastRefresh = null

  onMount(async () => {
    await loadTodaysSales()
    await loadMqStatus()
    await loadConsumers()
  })

  async function loadTodaysSales() {
    isLoadingSales = true
    try {
      todaysSales = await getTodaysSales()
      lastRefresh = new Date()
      console.log('📊 Bugünkü satışlar yüklendi:', todaysSales)
      showSuccess(`${todaysSales.length} satış kaydı yüklendi`)
    } catch (error) {
      console.error('❌ Satışlar yüklenirken hata:', error)
      showError('Satışlar yüklenirken hata oluştu')
    } finally {
      isLoadingSales = false
    }
  }

  function formatTime(dateString) {
    const date = new Date(dateString)
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  function formatCurrency(amount) {
    return amount.toLocaleString('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    })
  }

  function formatDate(date) {
    return date.toLocaleString('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // RabbitMQ state
  let mqStatus = null
  let isLoadingMq = false
  let consumers = []

  async function loadMqStatus() {
    isLoadingMq = true
    try {
      mqStatus = await window.electronAPI.rabbitmq.getStatus()
      console.log('🐰 RabbitMQ Status:', mqStatus)
    } catch (error) {
      console.error('❌ RabbitMQ status yüklenirken hata:', error)
      showError('RabbitMQ status yüklenirken hata oluştu')
    } finally {
      isLoadingMq = false
    }
  }

  async function loadConsumers() {
    try {
      consumers = await window.electronAPI.rabbitmq.getConsumers()
      console.log('🔄 Active Consumers:', consumers)
    } catch (error) {
      console.error('❌ Consumer bilgileri yüklenirken hata:', error)
      showError('Consumer bilgileri yüklenirken hata oluştu')
    }
  }

  async function retryConsumers() {
    try {
      const result = await window.electronAPI.rabbitmq.retryConsumers()
      console.log('🔄 Consumer retry result:', result)
      showSuccess('Consumer kurulumu yeniden denendi')
      await loadMqStatus()
      await loadConsumers()
    } catch (error) {
      console.error('❌ Consumer retry hatası:', error)
      showError('Consumer kurulumu retry edilirken hata oluştu')
    }
  }

  async function testConsumerSetup() {
    try {
      const result = await window.electronAPI.rabbitmq.testConsumerSetup()
      console.log('🧪 Consumer setup test result:', result)

      if (result.success) {
        showSuccess(`Consumer testi başarılı! ${result.consumersCount} consumer aktif.`)
      } else {
        showError(`Consumer testi başarısız: ${result.error}`)
      }

      await loadMqStatus()
      await loadConsumers()
    } catch (error) {
      console.error('❌ Consumer test hatası:', error)
      showError('Consumer testi çalıştırılırken hata oluştu')
    }
  }

  async function showDetailedChannelStats() {
    try {
      const detailedStats = await window.electronAPI.rabbitmq.getDetailedChannelStats()
      console.log('📊 Detailed Channel Stats:', detailedStats)
      showSuccess('Detaylı channel istatistikleri konsola yazdırıldı')
    } catch (error) {
      console.error('❌ Detailed channel stats hatası:', error)
      showError('Detaylı channel istatistikleri alınırken hata oluştu')
    }
  }

  async function forceRabbitMQInit() {
    try {
      console.log('🚀 Force initializing RabbitMQ...')
      const result = await window.electronAPI.rabbitmq.forceInit()
      console.log('🚀 Force init result:', result)

      if (result.success) {
        showSuccess('RabbitMQ zorla başlatıldı ve employee sync istendi!')
      } else {
        showError(`RabbitMQ zorla başlatma başarısız: ${result.error}`)
      }

      await loadMqStatus()
      await loadConsumers()
    } catch (error) {
      console.error('❌ Force RabbitMQ init hatası:', error)
      showError('RabbitMQ zorla başlatma işlemi başarısız oldu')
    }
  }

  async function checkQueues() {
    try {
      console.log('📊 Checking queue status...')
      const queueStatus = await window.electronAPI.rabbitmq.checkQueues()
      console.log('📊 Queue Status:', queueStatus)

      // Show important queue info
      if (queueStatus['employees']) {
        const employeeQueue = queueStatus['employees']
        if (employeeQueue.exists) {
          showSuccess(
            `Employee queue: ${employeeQueue.messageCount} mesaj, ${employeeQueue.consumerCount} consumer`
          )
        } else {
          showError('Employee queue bulunamadı!')
        }
      }

      showSuccess('Queue durumları konsola yazdırıldı')
    } catch (error) {
      console.error('❌ Queue kontrol hatası:', error)
      showError('Queue durumları kontrol edilirken hata oluştu')
    }
  }

  async function testEmployeeQueue() {
    try {
      console.log('👥 Testing employee queue...')
      const result = await window.electronAPI.rabbitmq.testEmployeeQueue()
      console.log('👥 Employee queue test result:', result)

      if (result.success) {
        if (result.processed) {
          showSuccess(
            `Employee mesajı işlendi! Queue'da ${result.queueInfo.messageCount} mesaj var.`
          )
        } else {
          showSuccess(result.message || 'Employee queue testi tamamlandı')
        }
      } else {
        showError(`Employee queue testi başarısız: ${result.error}`)
      }

      // Refresh other info
      await loadMqStatus()
      await loadConsumers()
    } catch (error) {
      console.error('❌ Employee queue test hatası:', error)
      showError('Employee queue testi çalıştırılırken hata oluştu')
    }
  }

  async function forceEmployeeFullSync() {
    try {
      console.log('🔄 Forcing employee full sync...')
      const result = await window.electronAPI.rabbitmq.forceEmployeeFullSync()
      console.log('🔄 Employee full sync result:', result)

      if (result.success) {
        showSuccess('Employee full sync zorlandı! Sync request gönderildi.')
      } else {
        showError(`Employee full sync başarısız: ${result.error}`)
      }

      // Refresh status
      await loadMqStatus()
      await loadConsumers()
    } catch (error) {
      console.error('❌ Employee full sync hatası:', error)
      showError('Employee full sync işlemi başarısız oldu')
    }
  }

  // ...existing code...
</script>

<div class="sales-page">
  <!-- TEST: SAYFA YÜKLENDİ -->
  <div style="background: blue; color: white; padding: 10px; margin: 10px;">
    <h1>🔵 TEST SAYFASI YÜKLÜ - {new Date().toLocaleString()}</h1>
    <p>URL: {window.location.hash}</p>
    <p>Route: /test</p>
  </div>

  <div class="hero light-theme-hero">
    <div class="hero-body">
      <div class="container">
        <h1 class="title light-theme-title">
          <span class="icon">
            <i class="fas fa-chart-line"></i>
          </span>
          Satışlar
        </h1>
        <h2 class="subtitle light-theme-subtitle">
          Bugünkü satış işlemlerini görüntüleyin ve yönetin
        </h2>
      </div>
    </div>
  </div>

  <div class="section">
    <div class="container">
      <!-- Sales Controls -->
      <div class="box light-theme-box mb-5">
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <h3 class="title is-4 light-theme-text">
                <span class="icon">
                  <i class="fas fa-list"></i>
                </span>
                Bugünkü Satışlar
              </h3>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <button
                class="button light-theme-button"
                on:click={loadTodaysSales}
                disabled={isLoadingSales}
              >
                <span class="icon">
                  <i class="fas fa-sync-alt"></i>
                </span>
                <span>Yenile</span>
              </button>
            </div>
            {#if lastRefresh}
              <div class="level-item">
                <small class="has-text-grey">
                  Son güncelleme: {formatDate(lastRefresh)}
                </small>
              </div>
            {/if}
          </div>
        </div>
      </div>

      <!-- Sales Content -->
      {#if isLoadingSales}
        <div class="box light-theme-box">
          <div class="has-text-centered">
            <span class="icon is-large">
              <i class="fas fa-spinner fa-pulse fa-2x"></i>
            </span>
            <p class="mt-3">Satışlar yükleniyor...</p>
          </div>
        </div>
      {:else if todaysSales.length === 0}
        <div class="box light-theme-box">
          <div class="notification is-info is-light">
            <span class="icon">
              <i class="fas fa-info-circle"></i>
            </span>
            <span>Bugün henüz satış yapılmamış.</span>
          </div>
        </div>
      {:else}
        <!-- Sales Table -->
        <div class="box light-theme-box">
          <div class="table-container">
            <table class="table is-fullwidth is-striped is-hoverable">
              <thead>
                <tr>
                  <th>Fiş No</th>
                  <th>Saat</th>
                  <th class="has-text-right">Tutar</th>
                  <th>Ödeme</th>
                  <th>Personel</th>
                </tr>
              </thead>
              <tbody>
                {#each todaysSales as sale (sale.id)}
                  <tr>
                    <td>
                      <span class="tag is-primary">{sale.receipt_number}</span>
                    </td>
                    <td>{formatTime(sale.created_at)}</td>
                    <td class="has-text-right">
                      <strong>{formatCurrency(sale.total_price)}</strong>
                    </td>
                    <td>
                      <span class="tag is-light">{sale.payment_methods || 'Bilinmiyor'}</span>
                    </td>
                    <td>
                      <span class="tag is-info is-light">
                        {sale.employee_full_name || sale.employee_code || 'Bilinmiyor'}
                      </span>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        </div>

        <!-- Sales Summary -->
        <div class="box light-theme-box">
          <h4 class="title is-5 light-theme-text">
            <span class="icon">
              <i class="fas fa-chart-bar"></i>
            </span>
            Günlük Özet
          </h4>
          <div class="level">
            <div class="level-item has-text-centered">
              <div>
                <p class="heading">Toplam Satış</p>
                <p class="title is-4">{todaysSales.length}</p>
              </div>
            </div>
            <div class="level-item has-text-centered">
              <div>
                <p class="heading">Toplam Tutar</p>
                <p class="title is-4">
                  {formatCurrency(todaysSales.reduce((sum, sale) => sum + sale.total_price, 0))}
                </p>
              </div>
            </div>
            <div class="level-item has-text-centered">
              <div>
                <p class="heading">Ortalama Satış</p>
                <p class="title is-4">
                  {formatCurrency(
                    todaysSales.reduce((sum, sale) => sum + sale.total_price, 0) /
                      todaysSales.length
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>
      {/if}

      <!-- RabbitMQ Status -->
      <div class="box light-theme-box mt-5">
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <h3 class="title is-4 light-theme-text">
                <span class="icon">
                  <i class="fas fa-rabbit"></i>
                </span>
                RabbitMQ Durumu
              </h3>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <button
                class="button light-theme-button"
                on:click={loadMqStatus}
                disabled={isLoadingMq}
              >
                <span class="icon">
                  <i class="fas fa-sync-alt"></i>
                </span>
                <span>Yenile</span>
              </button>
            </div>
          </div>
        </div>

        {#if isLoadingMq}
          <div class="has-text-centered">
            <span class="icon is-large">
              <i class="fas fa-spinner fa-pulse fa-2x"></i>
            </span>
            <p class="mt-3">RabbitMQ durumu yükleniyor...</p>
          </div>
        {:else if mqStatus}
          <div class="content">
            <p>
              <strong>Bağlantı Durumu:</strong>
              <span class="tag {mqStatus.connected ? 'is-success' : 'is-danger'}">
                {mqStatus.connected ? 'Bağlı' : 'Bağlı Değil'}
              </span>
            </p>
            <p>
              <strong>Aktif Consumer Sayısı:</strong>
              {mqStatus.consumers}
            </p>
            {#if mqStatus.channelPoolStats}
              <p>
                <strong>Channel Pool:</strong>
                {mqStatus.channelPoolStats.inUse}/{mqStatus.channelPoolStats.total} kullanımda,
                {mqStatus.channelPoolStats.idle} boşta ({mqStatus.channelPoolStats.utilization} doluluk)
              </p>
              <p>
                <strong>Ortalama Channel Yaşı:</strong>
                {mqStatus.channelPoolStats.averageAge}
              </p>
              {#if mqStatus.channelPoolStats.errorCount > 0}
                <p>
                  <strong>Toplam Hata Sayısı:</strong>
                  <span class="tag is-warning">{mqStatus.channelPoolStats.errorCount}</span>
                </p>
              {/if}
            {/if}
          </div>

          <!-- Consumer Management -->
          <div class="level mt-4">
            <div class="level-left">
              <div class="level-item">
                <button class="button is-info" on:click={loadConsumers}>
                  <span class="icon">
                    <i class="fas fa-refresh"></i>
                  </span>
                  <span>Consumer Bilgilerini Yenile</span>
                </button>
              </div>
              <div class="level-item">
                <button class="button is-success" on:click={testConsumerSetup}>
                  <span class="icon">
                    <i class="fas fa-vial"></i>
                  </span>
                  <span>Consumer Kurulumunu Test Et</span>
                </button>
              </div>
              <div class="level-item">
                <button class="button is-light" on:click={showDetailedChannelStats}>
                  <span class="icon">
                    <i class="fas fa-chart-bar"></i>
                  </span>
                  <span>Detaylı Channel İstatistikleri</span>
                </button>
              </div>
              <div class="level-item">
                <button class="button is-info is-light" on:click={checkQueues}>
                  <span class="icon">
                    <i class="fas fa-list-ul"></i>
                  </span>
                  <span>Queue Durumlarını Kontrol Et</span>
                </button>
              </div>
            </div>
            <div class="level-right">
              <div class="level-item">
                <button class="button is-success" on:click={testEmployeeQueue}>
                  <span class="icon">
                    <i class="fas fa-user-check"></i>
                  </span>
                  <span>Employee Queue Test & Process</span>
                </button>
              </div>
              <div class="level-item">
                <button class="button is-primary" on:click={forceEmployeeFullSync}>
                  <span class="icon">
                    <i class="fas fa-users-cog"></i>
                  </span>
                  <span>Force Employee Full Sync</span>
                </button>
              </div>
              <div class="level-item">
                <button class="button is-danger" on:click={forceRabbitMQInit}>
                  <span class="icon">
                    <i class="fas fa-rocket"></i>
                  </span>
                  <span>RabbitMQ Zorla Başlat & Employee Sync</span>
                </button>
              </div>
              <div class="level-item">
                <button class="button is-warning" on:click={retryConsumers}>
                  <span class="icon">
                    <i class="fas fa-redo"></i>
                  </span>
                  <span>Consumer Kurulumunu Yeniden Dene</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Consumers -->
          <div class="mt-4">
            <h4 class="title is-5 light-theme-text">
              <span class="icon">
                <i class="fas fa-cogs"></i>
              </span>
              Aktif Consumer'lar
            </h4>

            {#if consumers.length === 0}
              <div class="notification is-warning is-light">
                <span class="icon">
                  <i class="fas fa-exclamation-triangle"></i>
                </span>
                <span>Hiç aktif consumer yok. Consumer kurulumu yapılmamış olabilir.</span>
              </div>
            {:else}
              <div class="table-container">
                <table class="table is-fullwidth is-striped is-hoverable">
                  <thead>
                    <tr>
                      <th>Tip</th>
                      <th>Başlatıldığı Zaman</th>
                      <th>Çalışma Süresi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {#each consumers as consumer (consumer.type)}
                      <tr>
                        <td>
                          <span class="tag is-primary">{consumer.type}</span>
                        </td>
                        <td>
                          <span class="tag is-light">{consumer.started}</span>
                        </td>
                        <td>
                          <span class="tag is-info is-light">
                            {Math.round(consumer.uptime / 1000)} saniye
                          </span>
                        </td>
                      </tr>
                    {/each}
                  </tbody>
                </table>
              </div>
            {/if}
          </div>
        {:else}
          <div class="notification is-warning is-light">
            <span class="icon">
              <i class="fas fa-exclamation-triangle"></i>
            </span>
            <span>RabbitMQ durumu alınamadı.</span>
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>

<style>
  /* Light Theme Styles */
  .light-theme-hero {
    background: linear-gradient(
      135deg,
      var(--color-light-secondary),
      var(--color-light-secondary-gradient)
    );
    color: white;
  }

  .light-theme-title {
    color: white !important;
  }

  .light-theme-subtitle {
    color: rgba(255, 255, 255, 0.9) !important;
  }

  .light-theme-box {
    background-color: var(--color-light) !important;
    border: 1px solid var(--color-light-trd);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .light-theme-text {
    color: var(--color-light-text) !important;
  }

  /* Button Styles */
  .light-theme-button {
    background-color: var(--color-theme-light) !important;
    border-color: var(--color-theme-light) !important;
    color: white !important;
  }

  .light-theme-button:hover {
    background-color: #c91653 !important;
    border-color: #c91653 !important;
  }
</style>
