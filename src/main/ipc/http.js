/**
 * HTTP Request IPC Handlers
 * Handles all HTTP/HTTPS-related IPC communication for POS integration
 */

import { ipcMain } from 'electron'
import https from 'https'

export function httpHandlers() {
  // HTTPS request handler for POS communication
  ipcMain.handle('https:request', async (_, { hostname, port, path, method, payload }) => {
    return new Promise((resolve, reject) => {
      const payloadString = JSON.stringify(payload)

      const options = {
        hostname,
        port,
        path: `/${path}`,
        method,
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(payloadString),
        },
        rejectUnauthorized: false,
      }

      console.log(`[POS] Making ${method} request to: https://${hostname}:${port}/${path}`)
      console.log('[POS] Payload:', JSON.stringify(payload, null, 2))

      const req = https.request(options, res => {
        let responseData = ''

        res.on('data', chunk => {
          responseData += chunk
        })

        res.on('end', () => {
          try {
            console.log('[POS] Raw response:', responseData)
            const parsedResponse = JSON.parse(responseData)
            console.log('[POS] Parsed response:', parsedResponse)
            resolve(parsedResponse)
          } catch (error) {
            console.error('[POS] Error parsing response:', error)
            reject(new Error(`Response parsing error: ${error.message}`))
          }
        })
      })

      req.on('error', error => {
        console.error('[POS] Request error:', error)
        reject(error)
      })

      req.on('timeout', () => {
        console.error('[POS] Request timeout')
        req.destroy()
        reject(new Error('Request timeout'))
      })

      req.setTimeout(330000) // 10 second timeout pos cevap bekleme süresi

      if (payloadString) {
        req.write(payloadString)
      }

      req.end()
    })
  })

  console.log('✅ HTTP IPC handlers registered')
}
