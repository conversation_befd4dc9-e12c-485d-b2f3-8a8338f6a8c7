import bcrypt from 'bcryptjs'
import Database from 'better-sqlite3'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { DB_CONFIG } from './config.js'
import { applyDatabaseSchema } from './schema.js'

// Turkish character normalization utility
function normalizeTurkish(text) {
  if (!text) return ''
  return text
    .toLowerCase()
    .replace(/[İI]/g, 'i')
    .replace(/[Ğğ]/g, 'g')
    .replace(/[Üü]/g, 'u')
    .replace(/[Şş]/g, 's')
    .replace(/[Öö]/g, 'o')
    .replace(/[Çç]/g, 'c')
}

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

let db

/**
 * Initialize the database connection and apply schema
 * @returns {Database} The database instance
 */
function initDatabase() {
  try {
    // Create database directory if it doesn't exist
    const projectRoot = path.resolve(__dirname, '../../')
    const dataDir = path.join(projectRoot, DB_CONFIG.path)

    // Ensure data directory exists
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true })
      console.log(`📁 Created data directory: ${dataDir}`)
    }

    // Create database file path
    const dbPath = path.join(dataDir, DB_CONFIG.name)
    console.log(`📁 Database path: ${dbPath}`)

    // Initialize database connection
    db = new Database(dbPath)

    // Apply database schema from schema.js
    applyDatabaseSchema(db)

    console.log('✅ Database initialized successfully')
    return db
  } catch (error) {
    console.error('❌ Error initializing database:', error)
    throw error
  }
}

/**
 * Get the current database instance
 * @returns {Database} The database instance
 */
function getDatabase() {
  if (!db) {
    throw new Error('Database not initialized. Call initDatabase() first.')
  }
  return db
}

// ===== EMPLOYEE MANAGEMENT FUNCTIONS =====

/**
 * Get all employees from the database
 * @returns {Array} Array of employee objects
 */
function getAllEmployees() {
  try {
    const database = getDatabase()
    const stmt = database.prepare(`
      SELECT e.*, m.name as market_name
      FROM employees e
      LEFT JOIN market_id m ON e.market_id = m.id
      WHERE e.deleted_at IS NULL
      ORDER BY e.created_at DESC
    `)
    return stmt.all()
  } catch (error) {
    console.error('❌ Error getting all employees:', error)
    throw error
  }
}

/**
 * Authenticate employee login with bcrypt password verification
 * @param {string} code - Employee code
 * @param {string} password - Employee password (plain text)
 * @returns {Object|null} Employee object if authenticated, null otherwise
 */
async function authenticateEmployee(code, password) {
  try {
    const database = getDatabase()

    // First get the employee by code
    const stmt = database.prepare(`
      SELECT *
      FROM employees e
      WHERE e.code = ? AND e.deleted_at IS NULL
    `)
    const employee = stmt.get(code)

    if (!employee) {
      console.log(`🔐 Employee not found with code: ${code}`)
      return null
    }

    // Verify password with bcrypt
    const passwordMatch = await bcrypt.compare(password, employee.password)

    if (!passwordMatch) {
      console.log(`🔐 Password mismatch for employee: ${code}`)
      return null
    }

    // Update last login timestamp
    const updateStmt = database.prepare(`
      UPDATE employees SET last_login = CURRENT_TIMESTAMP WHERE id = ?
    `)
    updateStmt.run(employee.id)

    console.log(
      `🔐 Employee authenticated successfully: ${code} (${employee.name} ${employee.surname})`
    )
    return employee
  } catch (error) {
    console.error('❌ Error authenticating employee:', error)
    throw error
  }
}

// ===== INVENTORY MANAGEMENT FUNCTIONS =====

/**
 * Get all inventory items
 * @returns {Array} Array of inventory objects
 */
function getAllInventory() {
  try {
    const database = getDatabase()
    const stmt = database.prepare(`
      SELECT i.*, s.name as supplier_name, c.name as category_name
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      LEFT JOIN categories c ON i.category_code = c.code
      WHERE i.deleted_at IS NULL
      ORDER BY i.name ASC
    `)
    return stmt.all()
  } catch (error) {
    console.error('❌ Error getting inventory:', error)
    throw error
  }
}

/**
 * Search inventory by barcode
 * @param {string} barcode - Product barcode
 * @returns {Object|null} Inventory item if found
 */
function getInventoryByBarcode(barcode) {
  try {
    const database = getDatabase()

    // First, get the inventory code from the barcode table
    const barcodeStmt = database.prepare(`
      SELECT bar_stokkodu FROM BarkodTanimlari WHERE bar_kodu = ?
    `)
    const barcodeResult = barcodeStmt.get(barcode)

    if (!barcodeResult) {
      return null // Barcode not found
    }

    // Then get the inventory item using the inventory code
    const stmt = database.prepare(`
      SELECT i.*, s.name as supplier_name, c.name as category_name
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      LEFT JOIN categories c ON i.category_code = c.code
      WHERE i.inventory_code = ? AND i.deleted_at IS NULL
    `)
    return stmt.get(barcodeResult.bar_stokkodu)
  } catch (error) {
    console.error('❌ Error getting inventory by barcode:', error)
    throw error
  }
}

/**
 * Search inventory by multiple criteria
 * @param {string} searchTerm - Search term to match against name, barcode, or inventory_code
 * @returns {Array} Array of matching inventory items
 */

function searchInventory(searchTerm) {
  try {
    console.log('🔍 Searching inventory for:', searchTerm)

    if (!searchTerm || searchTerm.trim() === '') {
      return []
    }

    const database = getDatabase()
    const normalizedSearchTerm = normalizeTurkish(searchTerm.trim())

    // Check if search term is numeric (barcode or inventory code)
    const isNumber = !isNaN(Number(searchTerm))

    if (isNumber) {
      // Barcode search - try exact match first
      let inventoryCode = null

      try {
        // Handle weighted products (starting with 270)
        if (searchTerm.startsWith('270') && searchTerm.length > 7) {
          const shortParam = searchTerm.substring(0, 7)
          const barcodeQuery = `SELECT bar_stokkodu FROM BarkodTanimlari WHERE bar_kodu = ?`
          const barcodeResult = database.prepare(barcodeQuery).get(shortParam)

          if (barcodeResult) {
            inventoryCode = barcodeResult.bar_stokkodu
          }
        } else {
          // Normal barcode search
          const barcodeQuery = `SELECT bar_stokkodu FROM BarkodTanimlari WHERE bar_kodu = ?`
          const barcodeResult = database.prepare(barcodeQuery).get(searchTerm)

          if (barcodeResult) {
            inventoryCode = barcodeResult.bar_stokkodu
          } else {
            // Try as direct inventory_code
            const inventoryQuery = `SELECT inventory_code FROM inventory WHERE inventory_code = ? AND deleted_at IS NULL`
            const inventoryResult = database.prepare(inventoryQuery).get(searchTerm)

            if (inventoryResult) {
              inventoryCode = inventoryResult.inventory_code
            }
          }
        }

        // If inventory code found, get product details
        if (inventoryCode) {
          const productQuery = `
            SELECT
              i.id,
              i.inventory_code,
              i.name,
              i.unit,
              i.tax_percent,
              i.ctg_code,
              c.name as category_name,
              b.birimAdi,
              b.bar_kodu as barcode,
              COALESCE(s.Fiyati, i.price, 0) as price
            FROM inventory i
            JOIN category c ON c.ctg_code = i.ctg_code
            LEFT JOIN BarkodTanimlari b ON b.bar_stokkodu = i.inventory_code
            LEFT JOIN StokSatisFiyat s ON s.sfiyat_stokkod = i.inventory_code
            WHERE i.inventory_code = ? AND i.deleted_at IS NULL
            LIMIT 1
          `

          const productResult = database.prepare(productQuery).get(inventoryCode)

          console.log(productResult)

          if (productResult) {
            return [
              {
                id: productResult.id,
                inventory_code: productResult.inventory_code,
                name: productResult.name,
                unit: productResult.birimAdi || productResult.unit || 'Adet',
                tax_percent: productResult.tax_percent || 0,
                ctg_code: productResult.ctg_code,
                barcode: productResult.barcode,
                category_name: productResult.category_name,
                price: productResult.price || 0,
              },
            ]
          }
        }
      } catch (barcodeErr) {
        console.error('Barcode search error:', barcodeErr)
      }

      // If barcode not found, continue to text search
      console.log('Barcode not found, continuing to text search...')
    }

    // Enhanced text search for non-numeric terms or when barcode not found
    const words = normalizedSearchTerm.split(/\s+/).filter(w => w.length > 0)

    if (words.length === 0) {
      return []
    }

    // Simple normalized search query
    const searchQuery = `
      SELECT DISTINCT
        i.id,
        i.inventory_code,
        i.name,
        i.unit,
        i.tax_percent,
        i.ctg_code,
        c.name as category_name,
        b.birimAdi,
        b.bar_kodu as barcode,
        COALESCE(s.Fiyati, i.price, 0) as price
      FROM inventory i
      JOIN category c ON c.ctg_code = i.ctg_code
      LEFT JOIN BarkodTanimlari b ON b.bar_stokkodu = i.inventory_code
      LEFT JOIN StokSatisFiyat s ON s.sfiyat_stokkod = i.inventory_code
      WHERE i.deleted_at IS NULL
        AND (
          LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(i.name, 'İ', 'i'), 'Ğ', 'g'), 'Ü', 'u'), 'Ş', 's'), 'Ö', 'o'), 'Ç', 'c')) LIKE ?
          OR LOWER(i.inventory_code) LIKE ?
        )
      ORDER BY
        CASE WHEN LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(i.name, 'İ', 'i'), 'Ğ', 'g'), 'Ü', 'u'), 'Ş', 's'), 'Ö', 'o'), 'Ç', 'c')) LIKE ? THEN 1 ELSE 2 END,
        i.name
      LIMIT 50
    `

    const searchPattern = `%${normalizedSearchTerm}%`
    const startPattern = `${normalizedSearchTerm}%`

    const stmt = database.prepare(searchQuery)
    const results = stmt.all(searchPattern, searchPattern, startPattern)

    console.log(`🔍 Found ${results.length} inventory items`)

    return results.map(item => ({
      id: item.id,
      inventory_code: item.inventory_code,
      name: item.name,
      unit: item.birimAdi || item.unit || 'Adet',
      tax_percent: item.tax_percent || 0,
      ctg_code: item.ctg_code,
      barcode: item.barcode,
      category_name: item.category_name,
      price: item.price || 0,
    }))
  } catch (error) {
    console.error('❌ Error searching inventory:', error)
    return []
  }
}

// ===== SALES MANAGEMENT FUNCTIONS =====

/**
 * Save a complete sale transaction to the database
 * @param {Object} saleData - Sale transaction data
 * @returns {Object} Result with success status and sale ID
 */
function saveSaleTransaction(saleData) {
  try {
    const database = getDatabase()

    console.log('💾 saveSaleTransaction called with:', {
      employeeUuid: saleData.employeeUuid,
      userId: saleData.user_id,
      workstationId: saleData.workstationId,
      totalPrice: saleData.totalPrice,
      itemsCount: saleData.items?.length || 0,
      paymentsCount: saleData.payments?.length || 0,
    })

    // Validate that the employee exists and ensure we have a valid UUID
    let finalEmployeeUuid = null

    if (saleData.employeeUuid) {
      // First, try to find by UUID
      const checkEmployeeByUuid = database.prepare(
        'SELECT uuid, name, surname FROM employees WHERE uuid = ?'
      )
      const employeeByUuid = checkEmployeeByUuid.get(saleData.employeeUuid)

      if (employeeByUuid) {
        console.log('✅ Employee found by UUID:', employeeByUuid)
        finalEmployeeUuid = employeeByUuid.uuid
      } else {
        // If not found by UUID, try by ID (in case employeeUuid actually contains an ID)
        const checkEmployeeById = database.prepare(
          'SELECT uuid, name, surname FROM employees WHERE id = ?'
        )
        const employeeById = checkEmployeeById.get(saleData.employeeUuid)

        if (employeeById) {
          console.log('✅ Employee found by ID, using their UUID:', employeeById)
          finalEmployeeUuid = employeeById.uuid
        } else {
          console.error('❌ Employee not found for value:', saleData.employeeUuid)

          // List all available employees for debugging
          const allEmployees = database
            .prepare('SELECT id, uuid, code, name, surname FROM employees')
            .all()
          console.log('📋 Available employees in database:', allEmployees)

          throw new Error(`Employee not found for value: ${saleData.employeeUuid}`)
        }
      }
    } else {
      throw new Error('Employee UUID is required for sales')
    }

    console.log('🔐 Final employee UUID for sale:', finalEmployeeUuid)

    // Generate UUID for the sale
    const saleUuid = `sale-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    // Get next receipt number
    const receiptNumber = getNextReceiptNumber('sale')

    // Begin transaction
    const transaction = database.transaction(saleData => {
      // Insert main sale record
      const insertSale = database.prepare(`
        INSERT INTO sales (
          uuid, status, initiated_by, workstation_id, receipt_number,
          customer, discount, discount_type, original_price, total_price,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `)

      const saleResult = insertSale.run(
        saleUuid,
        saleData.status.status,
        finalEmployeeUuid, // Use the validated UUID
        saleData.workstationId || 'ws-001',
        receiptNumber,
        saleData.customerId || null,
        saleData.discount || 0,
        1, // discount_type: percentage
        saleData.originalPrice || saleData.totalPrice || saleData.total_amount || 0,
        saleData.totalPrice || saleData.total_amount || 0
      )

      // Insert sale items
      const insertSaleItem = database.prepare(`
        INSERT INTO sale_items (
          sale_uuid, inventory_code, quantity, unit_price, total_price,
          weight, weight_unit, discount, discount_type
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      saleData.items.forEach(item => {
        insertSaleItem.run(
          saleUuid,
          item.inventory_code,
          item.quantity,
          item.unit_price,
          item.total_price,
          item.weight || 0,
          item.weight_unit || null,
          item.discount || 0,
          1 // discount_type: percentage
        )
      })

      // Insert payments
      const insertPayment = database.prepare(`
        INSERT INTO payments (
          sale_uuid, payment_method, amount, created_at, updated_at
        ) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `)

      saleData.payments.forEach(payment => {
        const paymentMethodId = getPaymentMethodId(payment.method)
        insertPayment.run(saleUuid, paymentMethodId, payment.amount)
      })

      return { saleId: saleResult.lastInsertRowid, saleUuid, receiptNumber }
    })

    const result = transaction(saleData)

    console.log('✅ Sale transaction saved successfully:', {
      saleId: result.saleId,
      saleUuid: result.saleUuid,
      receiptNumber: result.receiptNumber,
      totalAmount: saleData.totalPrice,
    })

    return {
      success: true,
      saleId: result.saleId,
      saleUuid: result.saleUuid,
      receiptNumber: result.receiptNumber,
      message: 'Satış başarıyla kaydedildi',
    }
  } catch (error) {
    console.error('❌ Error saving sale transaction:', error)
    throw error
  }
}

/**
 * Get next receipt number for a given type
 * @param {string} type - Receipt type ('sale' or 'refund')
 * @returns {string} Next receipt number
 */
function getNextReceiptNumber(type) {
  try {
    const database = getDatabase()

    // Get current receipt number
    const stmt = database.prepare(`
      SELECT last_number FROM receipt_numbers WHERE type = ?
    `)
    let result = stmt.get(type)

    if (!result) {
      // Initialize receipt number if not exists
      const insertStmt = database.prepare(`
        INSERT INTO receipt_numbers (type, last_number) VALUES (?, 1)
      `)
      insertStmt.run(type)
      result = { last_number: 1 }
    }

    const nextNumber = result.last_number + 1

    // Update receipt number
    const updateStmt = database.prepare(`
      UPDATE receipt_numbers SET last_number = ?, updated_at = CURRENT_TIMESTAMP WHERE type = ?
    `)
    updateStmt.run(nextNumber, type)

    // Format receipt number (e.g., "0000000001")
    return nextNumber.toString().padStart(10, '0')
  } catch (error) {
    console.error('❌ Error getting next receipt number:', error)
    throw error
  }
}

/**
 * Get payment method ID by method name
 * @param {string} methodName - Payment method name
 * @returns {number} Payment method ID
 */
function getPaymentMethodId(methodName) {
  try {
    const database = getDatabase()

    // Map method names to database values
    const methodMap = {
      cash: 'Nakit',
      'credit-card': 'Kredi Kartı',
      'meal-card': 'Yemek Kartı',
      'cash-drawer': 'Nakit',
    }

    const dbMethodName = methodMap[methodName] || methodName

    // Get or create payment method
    const stmt = database.prepare(`
      SELECT id FROM payment_methods WHERE name = ?
    `)
    const result = stmt.get(dbMethodName)

    if (!result) {
      // Create payment method if not exists
      const insertStmt = database.prepare(`
        INSERT INTO payment_methods (name) VALUES (?)
      `)
      const insertResult = insertStmt.run(dbMethodName)
      return insertResult.lastInsertRowid
    }

    return result.id
  } catch (error) {
    console.error('❌ Error getting payment method ID:', error)
    throw error
  }
}

/**
 * Get today's sales transactions with employee and payment information
 * @returns {Array} Array of today's sales with details
 */
function getTodaysSales() {
  try {
    const database = getDatabase()

    // Get today's date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0]

    const stmt = database.prepare(`
      SELECT
        s.id,
        s.uuid,
        s.receipt_number,
        s.total_price,
        s.created_at,
        e.name as employee_name,
        e.surname as employee_surname,
        e.code as employee_code
      FROM sales s
      LEFT JOIN employees e ON s.initiated_by = e.uuid
      WHERE date(s.created_at) = ?
        AND s.deleted_at IS NULL
      ORDER BY s.created_at DESC
    `)

    const sales = stmt.all(today)

    // Get payment methods for each sale
    const paymentStmt = database.prepare(`
      SELECT
        p.payment_method,
        p.amount,
        pm.name as method_name
      FROM payments p
      LEFT JOIN payment_methods pm ON p.payment_method = pm.id
      WHERE p.sale_uuid = ?
        AND p.deleted_at IS NULL
    `)

    // Enhance sales with payment information
    const enhancedSales = sales.map(sale => {
      const payments = paymentStmt.all(sale.uuid)

      return {
        ...sale,
        payments,
        payment_methods: payments.map(p => p.method_name).join(', '),
        employee_full_name: `${sale.employee_name} ${sale.employee_surname}`.trim(),
      }
    })

    console.log(`📊 Retrieved ${enhancedSales.length} sales for today (${today})`)

    return enhancedSales
  } catch (error) {
    console.error("❌ Error getting today's sales:", error)
    throw error
  }
}

/**
 * Get sales transactions with filtering, sorting, and pagination
 * @param {Object} options - Query options
 * @returns {Object} Sales data with pagination info
 */
function getSalesWithFilters(options = {}) {
  try {
    const database = getDatabase()

    const {
      dateFrom,
      dateTo,
      searchTerm = '',
      sortBy = 'created_at',
      sortOrder = 'DESC',
      page = 1,
      limit = 50,
    } = options

    const whereConditions = ['s.deleted_at IS NULL']
    const params = []

    // Date filtering
    if (dateFrom) {
      whereConditions.push('date(s.created_at) >= ?')
      params.push(dateFrom)
    }
    if (dateTo) {
      whereConditions.push('date(s.created_at) <= ?')
      params.push(dateTo)
    }

    // Search filtering
    if (searchTerm) {
      whereConditions.push(`(
        s.receipt_number LIKE ? OR
        e.name LIKE ? OR
        e.surname LIKE ? OR
        e.code LIKE ?
      )`)
      const searchPattern = `%${searchTerm}%`
      params.push(searchPattern, searchPattern, searchPattern, searchPattern)
    }

    const whereClause = whereConditions.join(' AND ')

    // Count total records
    const countQuery = `
      SELECT COUNT(*) as total
      FROM sales s
      LEFT JOIN employees e ON s.initiated_by = e.uuid
      WHERE ${whereClause}
    `
    const totalResult = database.prepare(countQuery).get(...params)
    const total = totalResult.total

    // Calculate pagination
    const offset = (page - 1) * limit
    const totalPages = Math.ceil(total / limit)

    // Get sales data
    const salesQuery = `
      SELECT
        s.id,
        s.uuid,
        s.receipt_number,
        s.total_price,
        s.created_at,
        e.name as employee_name,
        e.surname as employee_surname,
        e.code as employee_code
      FROM sales s
      LEFT JOIN employees e ON s.initiated_by = e.uuid
      WHERE ${whereClause}
      ORDER BY s.${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `

    const sales = database.prepare(salesQuery).all(...params, limit, offset)

    // Get payment methods for each sale
    const paymentStmt = database.prepare(`
      SELECT
        p.payment_method,
        p.amount,
        pm.name as method_name
      FROM payments p
      LEFT JOIN payment_methods pm ON p.payment_method = pm.id
      WHERE p.sale_uuid = ?
        AND p.deleted_at IS NULL
    `)

    // Enhance sales with payment information
    const enhancedSales = sales.map(sale => {
      const payments = paymentStmt.all(sale.uuid)

      return {
        ...sale,
        payments,
        payment_methods: payments.map(p => p.method_name).join(', '),
        employee_full_name: `${sale.employee_name} ${sale.employee_surname}`.trim(),
      }
    })

    console.log(`📊 Retrieved ${enhancedSales.length} sales (page ${page}/${totalPages})`)

    return {
      sales: enhancedSales,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    }
  } catch (error) {
    console.error('❌ Error getting sales with filters:', error)
    throw error
  }
}

/**
 * Get detailed sale information including items
 * @param {string} saleUuid - Sale UUID
 * @returns {Object} Complete sale details with items
 */
function getSaleDetails(saleUuid) {
  try {
    const database = getDatabase()

    // Get sale header information
    const saleQuery = `
      SELECT
        s.id,
        s.uuid,
        s.receipt_number,
        s.total_price,
        s.original_price,
        s.discount,
        s.created_at,
        e.name as employee_name,
        e.surname as employee_surname,
        e.code as employee_code
      FROM sales s
      LEFT JOIN employees e ON s.initiated_by = e.uuid
      WHERE s.uuid = ? AND s.deleted_at IS NULL
    `

    const sale = database.prepare(saleQuery).get(saleUuid)

    if (!sale) {
      throw new Error('Satış bulunamadı')
    }

    // Get sale items with inventory information
    const itemsQuery = `
      SELECT
        si.id,
        si.inventory_code,
        si.quantity,
        si.unit_price,
        si.total_price,
        si.weight,
        si.weight_unit,
        si.discount,
        i.name as item_name,
        i.unit as item_unit,
        fai.barcode
      FROM sale_items si
      LEFT JOIN inventory i ON si.inventory_code = i.inventory_code
      LEFT JOIN fast_access_items fai ON si.inventory_code = fai.inventory_code AND fai.deleted_at IS NULL
      WHERE si.sale_uuid = ?
      ORDER BY si.id
    `

    const items = database.prepare(itemsQuery).all(saleUuid)

    // Get payment information
    const paymentsQuery = `
      SELECT
        p.id,
        p.payment_method,
        p.amount,
        pm.name as method_name
      FROM payments p
      LEFT JOIN payment_methods pm ON p.payment_method = pm.id
      WHERE p.sale_uuid = ? AND p.deleted_at IS NULL
      ORDER BY p.id
    `

    const payments = database.prepare(paymentsQuery).all(saleUuid)

    // Enhance sale with additional information
    const enhancedSale = {
      ...sale,
      employee_full_name: `${sale.employee_name} ${sale.employee_surname}`.trim(),
      items: items.map(item => ({
        ...item,
        item_display_name: item.item_name || item.inventory_code,
        has_weight: item.weight && item.weight > 0,
        formatted_quantity:
          item.weight && item.weight > 0
            ? `${item.weight.toLocaleString('tr-TR')} ${item.weight_unit || 'KG'}`
            : item.quantity.toString(),
        unit_display:
          item.weight && item.weight > 0 ? item.weight_unit || 'KG' : item.item_unit || 'Adet',
      })),
      payments,
      payment_summary: payments.reduce((acc, payment) => {
        acc[payment.method_name] = (acc[payment.method_name] || 0) + payment.amount
        return acc
      }, {}),
      total_items: items.length,
      total_quantity: items.reduce((sum, item) => sum + item.quantity, 0),
    }

    console.log(`📋 Sale details retrieved for ${sale.receipt_number}:`, {
      items: items.length,
      payments: payments.length,
      total: sale.total_price,
    })

    return enhancedSale
  } catch (error) {
    console.error('❌ Error getting sale details:', error)
    throw error
  }
}

/**
 * Import user and permission data from JSON files
 * @param {Object} data - Object containing employees, roles, permissions data
 * @returns {Object} Import result with statistics
 */
function importUserAndPermissionData(data) {
  try {
    const database = getDatabase()

    console.log('📥 Starting import of user and permission data...')

    const stats = {
      employees: 0,
      roles: 0,
      permissions: 0,
      employee_roles: 0,
      role_permissions: 0,
    }

    // Begin transaction for data import
    const transaction = database.transaction(data => {
      // Import employees if provided
      if (data.employees && Array.isArray(data.employees)) {
        const insertEmployeeStmt = database.prepare(`
          INSERT OR REPLACE INTO employees (
            id, uuid, code, password, name, surname, address, phone,
            market_id, deleted_at, last_login, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `)

        for (const employee of data.employees) {
          insertEmployeeStmt.run(
            employee.id,
            employee.uuid,
            employee.code,
            employee.password,
            employee.name,
            employee.surname,
            employee.address,
            employee.phone,
            employee.market_id,
            employee.deleted_at,
            employee.last_login,
            employee.created_at,
            employee.updated_at
          )
          stats.employees++
        }
        console.log(`✅ Imported ${stats.employees} employees`)
      }

      // Import roles if provided
      if (data.roles && Array.isArray(data.roles)) {
        const insertRoleStmt = database.prepare(`
          INSERT OR REPLACE INTO roles (
            id, uuid, name, deleted_at, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?)
        `)

        for (const role of data.roles) {
          insertRoleStmt.run(
            role.id,
            role.uuid,
            role.name,
            role.deleted_at,
            role.created_at,
            role.updated_at
          )
          stats.roles++
        }
        console.log(`✅ Imported ${stats.roles} roles`)
      }

      // Import permissions if provided
      if (data.permissions && Array.isArray(data.permissions)) {
        const insertPermissionStmt = database.prepare(`
          INSERT OR REPLACE INTO permissions (
            id, uuid, name, code, description, deleted_at, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `)

        for (const permission of data.permissions) {
          insertPermissionStmt.run(
            permission.id,
            permission.uuid,
            permission.name,
            permission.code,
            permission.description,
            permission.deleted_at,
            permission.created_at,
            permission.updated_at
          )
          stats.permissions++
        }
        console.log(`✅ Imported ${stats.permissions} permissions`)
      }

      // Import employee roles if provided
      if (data.employee_roles && Array.isArray(data.employee_roles)) {
        const insertEmployeeRoleStmt = database.prepare(`
          INSERT OR REPLACE INTO employee_roles (
            id, employee_id, role_id, deleted_at
          ) VALUES (?, ?, ?, ?)
        `)

        for (const employeeRole of data.employee_roles) {
          insertEmployeeRoleStmt.run(
            employeeRole.id,
            employeeRole.employee_id,
            employeeRole.role_id,
            employeeRole.deleted_at
          )
          stats.employee_roles++
        }
        console.log(`✅ Imported ${stats.employee_roles} employee roles`)
      }

      // Import role permissions if provided
      if (data.role_permissions && Array.isArray(data.role_permissions)) {
        const insertRolePermissionStmt = database.prepare(`
          INSERT OR REPLACE INTO role_permissions (
            id, role_id, permission_code, deleted_at
          ) VALUES (?, ?, ?, ?)
        `)

        for (const rolePermission of data.role_permissions) {
          insertRolePermissionStmt.run(
            rolePermission.id,
            rolePermission.role_id,
            rolePermission.permission_code,
            rolePermission.deleted_at
          )
          stats.role_permissions++
        }
        console.log(`✅ Imported ${stats.role_permissions} role permissions`)
      }
    })

    // Execute the transaction
    transaction(data)

    console.log('✅ User and permission data import completed successfully')
    return stats
  } catch (error) {
    console.error('❌ Error importing user and permission data:', error)
    throw error
  }
}

/**
 * Get today's in-progress sale
 * @returns {Object|null} In-progress sale or null if none found
 */
function getTodaysInProgressSale() {
  console.log('****')

  try {
    const database = getDatabase()
    const today = new Date().toISOString().split('T')[0]

    // First, get the in-progress sale
    const saleStmt = database.prepare(`
      SELECT id, uuid, receipt_number, total_price, created_at
      FROM sales
      WHERE date(created_at) = ?
        AND status = 1
        AND deleted_at IS NULL
      ORDER BY created_at DESC
      LIMIT 1
    `)

    const saleRow = saleStmt.get(today)

    if (!saleRow) return null

    // Then get the sale items
    const itemsStmt = database.prepare(`
      SELECT
        si.inventory_code, si.quantity, si.unit_price, si.total_price,
        i.name, i.unit, i.id as inventory_id
      FROM sale_items si
      LEFT JOIN inventory i ON si.inventory_code = i.inventory_code
      WHERE si.sale_uuid = ? AND si.deleted_at IS NULL
    `)

    const itemRows = itemsStmt.all(saleRow.uuid)

    const sale = {
      id: saleRow.id,
      uuid: saleRow.uuid,
      receipt_number: saleRow.receipt_number,
      total_price: saleRow.total_price,
      created_at: saleRow.created_at,
      items: itemRows.map(row => ({
        id: row.inventory_id,
        inventory_code: row.inventory_code,
        name: row.name,
        unit: row.unit || 'adet',
        barcode: row.inventory_code || '', // Use inventory_code as barcode fallback since barcode column doesn't exist
        quantity: row.quantity,
        price: row.unit_price,
        total: row.total_price,
      })),
    }

    return sale
  } catch (error) {
    console.error("❌ Error getting today's in-progress sale:", error)
    throw error
  }
}

/**
 * Create a new in-progress sale
 * @param {Object} saleData - Initial sale data
 * @returns {Object} Created sale info
 */
function createInProgressSale(saleData) {
  try {
    const database = getDatabase()
    const saleUuid = `sale-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const receiptNumber = getNextReceiptNumber('sale')

    const insertSale = database.prepare(`
      INSERT INTO sales (
        uuid, status, initiated_by, workstation_id, receipt_number,
        customer, discount, discount_type, original_price, total_price,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `)

    const result = insertSale.run(
      saleUuid,
      1, // status: in_progress
      saleData.employeeUuid,
      saleData.workstationId || 'ws-001',
      receiptNumber,
      saleData.customerId || null,
      0, // discount
      1, // discount_type
      0, // original_price - will be updated when items are added
      0 // total_price - will be updated when items are added
    )

    return {
      success: true,
      saleId: result.lastInsertRowid,
      saleUuid,
      receiptNumber,
      message: 'In-progress sale created',
    }
  } catch (error) {
    console.error('❌ Error creating in-progress sale:', error)
    throw error
  }
}

/**
 * Add item to in-progress sale
 * @param {string} saleUuid - Sale UUID
 * @param {Object} item - Item to add
 * @returns {Object} Result of operation
 */
function addItemToInProgressSale(saleUuid, item) {
  try {
    const database = getDatabase()

    const transaction = database.transaction(() => {
      // Check if item already exists in sale
      const existingItem = database
        .prepare(
          `
        SELECT quantity FROM sale_items
        WHERE sale_uuid = ? AND inventory_code = ?
      `
        )
        .get(saleUuid, item.inventory_code)

      if (existingItem) {
        // Update quantity
        const newQuantity = existingItem.quantity + item.quantity
        const newTotal = newQuantity * item.price

        database
          .prepare(
            `
          UPDATE sale_items
          SET quantity = ?, total_price = ?
          WHERE sale_uuid = ? AND inventory_code = ?
        `
          )
          .run(newQuantity, newTotal, saleUuid, item.inventory_code)
      } else {
        database
          .prepare(
            `
          INSERT INTO sale_items (
            sale_uuid, inventory_code, quantity, unit_price, total_price,
            weight, weight_unit, discount, discount_type
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `
          )
          .run(
            saleUuid,
            item.inventory_code,
            item.quantity,
            item.unit_price,
            item.quantity * item.unit_price,
            0,
            null,
            0,
            1
          )
      }

      // Update sale totals
      const totalResult = database
        .prepare(
          `
        SELECT SUM(total_price) as total FROM sale_items WHERE sale_uuid = ?
      `
        )
        .get(saleUuid)

      const totalPrice = totalResult.total || 0
      database
        .prepare(
          `
        UPDATE sales SET original_price = ?, total_price = ?, updated_at = CURRENT_TIMESTAMP
        WHERE uuid = ?
      `
        )
        .run(totalPrice, totalPrice, saleUuid)

      return totalPrice
    })

    const totalPrice = transaction()

    return {
      success: true,
      totalPrice,
      message: 'Item added to sale',
    }
  } catch (error) {
    console.error('❌ Error adding item to in-progress sale:', error)
    throw error
  }
}

/**
 * Complete in-progress sale
 * @param {string} saleUuid - Sale UUID
 * @param {Array} payments - Payment information
 * @returns {Object} Result of operation
 */
function completeInProgressSale(saleUuid, payments) {
  try {
    const database = getDatabase()

    const transaction = database.transaction(() => {
      // Update sale status to completed
      database
        .prepare(
          `
        UPDATE sales SET status = 4, updated_at = CURRENT_TIMESTAMP WHERE uuid = ?
      `
        )
        .run(saleUuid)

      // Insert payments
      const insertPayment = database.prepare(`
        INSERT INTO payments (
          sale_uuid, payment_method, amount, created_at, updated_at
        ) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `)

      payments.forEach(payment => {
        const paymentMethodId = getPaymentMethodId(payment.method)
        insertPayment.run(saleUuid, paymentMethodId, payment.amount)
      })

      return true
    })

    transaction()

    return {
      success: true,
      message: 'Sale completed successfully',
    }
  } catch (error) {
    console.error('❌ Error completing sale:', error)
    throw error
  }
}

/**
 * Generate next sales number
 * @returns {string} Next sales number
 */
function generateSalesNumber() {
  try {
    const database = getDatabase()
    const today = new Date().toISOString().split('T')[0].replace(/-/g, '')

    const lastSale = database
      .prepare(
        `
      SELECT receipt_number FROM sales
      WHERE date(created_at) = date('now')
      ORDER BY id DESC LIMIT 1
    `
      )
      .get()

    let nextNumber = 1
    if (lastSale && lastSale.receipt_number) {
      const lastNumber = parseInt(lastSale.receipt_number.split('-').pop())
      if (!isNaN(lastNumber)) {
        nextNumber = lastNumber + 1
      }
    }

    return `${today}-${nextNumber.toString().padStart(4, '0')}`
  } catch (error) {
    console.error('❌ Error generating sales number:', error)
    return `${Date.now()}-0001`
  }
}

/**
 * Get current transaction sequence for POS device
 * @param {string} serialNumber - POS device serial number
 * @returns {number} Current transaction sequence
 */
function getPOSTransactionSequence(serialNumber = 'PAV600000327') {
  try {
    const database = getDatabase()

    const stmt = database.prepare(`
      SELECT transaction_sequence FROM pos_devices WHERE serial_number = ?
    `)
    const result = stmt.get(serialNumber)

    if (!result) {
      // Initialize POS device if not exists
      const insertStmt = database.prepare(`
        INSERT INTO pos_devices (processor_id, serial_number, ip_address, transaction_sequence, is_paired)
        VALUES (1, ?, '************', 1, 0)
      `)
      insertStmt.run(serialNumber)
      return 1
    }

    return result.transaction_sequence
  } catch (error) {
    console.error('❌ Error getting POS transaction sequence:', error)
    return 1
  }
}

/**
 * Update transaction sequence for POS device
 * @param {string} serialNumber - POS device serial number
 * @param {number} sequence - New transaction sequence
 */
function updatePOSTransactionSequence(serialNumber, sequence) {
  try {
    const database = getDatabase()

    const stmt = database.prepare(`
      UPDATE pos_devices
      SET transaction_sequence = ?, updated_at = CURRENT_TIMESTAMP
      WHERE serial_number = ?
    `)
    stmt.run(sequence, serialNumber)

    console.log(`📊 Database sequence updated: ${serialNumber} -> ${sequence}`)
  } catch (error) {
    console.error('❌ Error updating POS transaction sequence:', error)
  }
}

/**
 * Get daily sales report for a specific date
 * @param {string} reportDate - Date in YYYY-MM-DD format
 * @returns {Object} Daily report data
 */
function getDailyReport(reportDate) {
  console.log(`📊 Getting daily report for: ${reportDate}`)

  try {
    const db = getDatabase()

    // First check if we have any sales data at all
    const totalSalesCount = db.prepare('SELECT COUNT(*) as count FROM sales').get()
    console.log(`📊 Total sales in database: ${totalSalesCount.count}`)

    // Check sales for the specific date
    const dateSalesCount = db
      .prepare('SELECT COUNT(*) as count FROM sales WHERE DATE(created_at) = ?')
      .get(reportDate)
    console.log(`📊 Sales for date ${reportDate}: ${dateSalesCount.count}`)

    // Show some sample sales
    const sampleSales = db
      .prepare('SELECT id, created_at, total_price, status FROM sales LIMIT 5')
      .all()
    console.log(`📊 Sample sales:`, sampleSales)

    // Get payment type summary - we need to get payment info from payments table
    const paymentSummary = db
      .prepare(
        `
      SELECT
        pm.name as payment_type,
        COUNT(*) as count,
        SUM(p.amount) as total
      FROM payments p
      LEFT JOIN payment_methods pm ON p.payment_method = pm.id
      LEFT JOIN sales s ON p.sale_uuid = s.uuid
      WHERE DATE(s.created_at) = ? AND s.status = 2 AND p.deleted_at IS NULL
      GROUP BY pm.name
      ORDER BY total DESC
    `
      )
      .all(reportDate)

    // Get daily total (completed sales only - status 2 = completed)
    const dailyTotalResult = db
      .prepare(
        `
      SELECT COALESCE(SUM(total_price), 0) as total
      FROM sales
      WHERE DATE(created_at) = ? AND status = 2
    `
      )
      .get(reportDate)
    const dailyTotal = dailyTotalResult?.total || 0

    // Get cancel total (status 3 = cancelled)
    const cancelTotalResult = db
      .prepare(
        `
      SELECT COALESCE(SUM(total_price), 0) as total
      FROM sales
      WHERE DATE(created_at) = ? AND status = 3
    `
      )
      .get(reportDate)
    const cancelTotal = cancelTotalResult?.total || 0

    // Get refund total - from sale_refunds table
    const refundTotalResult = db
      .prepare(
        `
      SELECT COALESCE(SUM(sr.total_price), 0) as total
      FROM sale_refunds sr
      LEFT JOIN sales s ON sr.sale_uuid = s.uuid
      WHERE DATE(sr.created_at) = ?
    `
      )
      .get(reportDate)
    const refundTotal = refundTotalResult?.total || 0

    // Get user sales summary
    const userSales = db
      .prepare(
        `
      SELECT
        COALESCE(e.name || ' ' || e.surname, e.code, 'Bilinmiyor') as username,
        COUNT(*) as count,
        SUM(s.total_price) as total
      FROM sales s
      LEFT JOIN employees e ON s.initiated_by = e.uuid
      WHERE DATE(s.created_at) = ? AND s.status = 2
      GROUP BY s.initiated_by, e.name, e.surname, e.code
      ORDER BY total DESC
    `
      )
      .all(reportDate)

    // Get all sales for the day
    const allSales = db
      .prepare(
        `
      SELECT
        s.id,
        s.uuid,
        s.receipt_number,
        s.total_price as total_amount,
        s.status,
        s.created_at,
        COALESCE(e.name || ' ' || e.surname, e.code, 'Bilinmiyor') as username,
        CASE
          WHEN s.status = 1 THEN 'in_progress'
          WHEN s.status = 2 THEN 'completed'
          WHEN s.status = 3 THEN 'cancelled'
          ELSE 'unknown'
        END as status_text,
        GROUP_CONCAT(pm.name, ', ') as payment_type
      FROM sales s
      LEFT JOIN employees e ON s.initiated_by = e.uuid
      LEFT JOIN payments p ON s.uuid = p.sale_uuid AND p.deleted_at IS NULL
      LEFT JOIN payment_methods pm ON p.payment_method = pm.id
      WHERE DATE(s.created_at) = ?
      GROUP BY s.id, s.uuid, s.receipt_number, s.total_price, s.status, s.created_at, e.name, e.surname, e.code
      ORDER BY s.created_at DESC
    `
      )
      .all(reportDate)

    return {
      paymentSummary,
      dailyTotal,
      cancelTotal,
      refundTotal,
      userSales,
      allSales,
    }
  } catch (error) {
    console.error('❌ Error getting daily report:', error)
    throw error
  }
}

/**
 * Create test sales data for today
 */
function createTestSalesData() {
  console.log('📊 Creating test sales data...')

  try {
    const db = getDatabase()
    const today = new Date().toISOString().split('T')[0]

    // First, ensure we have payment methods
    const insertPaymentMethod = db.prepare(`
      INSERT OR IGNORE INTO payment_methods (name) VALUES (?)
    `)
    insertPaymentMethod.run('Nakit')
    insertPaymentMethod.run('Kredi Kartı')

    // Get payment method IDs
    const getPaymentMethodId = db.prepare('SELECT id FROM payment_methods WHERE name = ?')
    const cashId = getPaymentMethodId.get('Nakit').id
    const cardId = getPaymentMethodId.get('Kredi Kartı').id

    // Get or create a workstation
    const insertWorkstation = db.prepare(`
      INSERT OR IGNORE INTO workstation_id (uuid, wsno) VALUES (?, ?)
    `)
    insertWorkstation.run('test-workstation-1', 'WS001')
    const workstationUuid = 'test-workstation-1'

    // Get or create test employee
    const insertEmployee = db.prepare(`
      INSERT OR IGNORE INTO employees (uuid, code, password, name, surname) VALUES (?, ?, ?, ?, ?)
    `)
    insertEmployee.run('test-employee-1', 'TEST001', 'test123', 'Test', 'Kullanıcı')
    const testEmployeeUuid = 'test-employee-1'

    // Insert test sales
    const insertSale = db.prepare(`
      INSERT OR IGNORE INTO sales (
        uuid, receipt_number, total_price, original_price, status, created_at, workstation_id, initiated_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `)

    // Insert payments
    const insertPayment = db.prepare(`
      INSERT OR IGNORE INTO payments (
        sale_uuid, payment_method, amount, created_at
      ) VALUES (?, ?, ?, ?)
    `)

    const testSales = [
      {
        uuid: 'test-sale-1',
        receipt: 'TST001',
        amount: 150.5,
        status: 2,
        payment: cashId,
        time: '10:30:00',
      },
      {
        uuid: 'test-sale-2',
        receipt: 'TST002',
        amount: 75.25,
        status: 2,
        payment: cardId,
        time: '11:15:00',
      },
      {
        uuid: 'test-sale-3',
        receipt: 'TST003',
        amount: 200.0,
        status: 3,
        payment: cashId,
        time: '12:00:00',
      },
      {
        uuid: 'test-sale-4',
        receipt: 'TST004',
        amount: 50.75,
        status: 2,
        payment: cardId,
        time: '13:30:00',
      },
      {
        uuid: 'test-sale-5',
        receipt: 'TST005',
        amount: 125.0,
        status: 2,
        payment: cashId,
        time: '14:00:00',
      },
    ]

    const transaction = db.transaction(() => {
      for (const sale of testSales) {
        try {
          const datetime = `${today} ${sale.time}`

          // Insert sale
          insertSale.run(
            sale.uuid,
            sale.receipt,
            sale.amount,
            sale.amount,
            sale.status,
            datetime,
            workstationUuid,
            testEmployeeUuid
          )

          // Insert payment (only for completed sales)
          if (sale.status === 2) {
            insertPayment.run(sale.uuid, sale.payment, sale.amount, datetime)
          }

          console.log(`📊 Created test sale: ${sale.receipt} - ${sale.amount} TL`)
        } catch (error) {
          if (!error.message.includes('UNIQUE constraint failed')) {
            throw error
          }
          console.log(`📊 Test sale ${sale.receipt} already exists`)
        }
      }
    })

    transaction()
    console.log('📊 Test sales data created successfully')
  } catch (error) {
    console.error('❌ Error creating test sales data:', error)
    throw error
  }
}

// Export all functions
export {
  addItemToInProgressSale,
  authenticateEmployee,
  completeInProgressSale,
  createInProgressSale,
  createTestSalesData,
  generateSalesNumber,
  // Employee management
  getAllEmployees,
  // Inventory management
  getAllInventory,
  getDailyReport,
  getDatabase,
  getInventoryByBarcode,
  getNextReceiptNumber,
  getPaymentMethodId,
  getPOSTransactionSequence,
  getSaleDetails,
  getSalesWithFilters,
  getTodaysInProgressSale,
  getTodaysSales,
  importUserAndPermissionData,
  // Database initialization
  initDatabase,
  // Sales management
  saveSaleTransaction,
  searchInventory,
  updatePOSTransactionSequence,
}

// Default export for direct database access
export default getDatabase
